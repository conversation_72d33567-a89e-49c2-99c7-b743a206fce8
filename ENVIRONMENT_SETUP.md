# Environment Variable Management

## Overview

The Knowledge Quest Receiver application uses a centralized environment variable management system that provides robust loading, validation, and configuration capabilities. This system supports multiple environments, comprehensive validation, and intelligent fallback mechanisms.

## Features

### 🔧 **Centralized Configuration**
- Single source of truth for all environment variables
- Comprehensive validation with custom validators
- Environment-specific configuration support
- Intelligent fallback mechanisms

### 🛡️ **Security & Validation**
- Automatic masking of sensitive values in logs
- Environment-specific validation rules (stricter for production)
- Type validation (string, integer, boolean, port numbers)
- Required vs optional variable handling

### 🌍 **Multi-Environment Support**
- Environment-specific .env files (`.env.development`, `.env.staging`, `.env.production`)
- Automatic environment detection
- Fallback chain for configuration files

### 📊 **Enhanced Logging & Feedback**
- Clear success/failure messages
- Detailed configuration display
- Masked sensitive information
- Comprehensive error reporting

## Environment Files

The system supports multiple environment files with the following priority order:

1. `.env.{environment}` (e.g., `.env.production`)
2. `.env.{environment}.local` (e.g., `.env.development.local`)
3. `.env.local`
4. `.env` (default)

## Usage

### Quick Setup (Recommended)

For most applications, use the quick setup function:

```python
from helpers.env_loader import quick_env_setup

# Quick environment setup with validation
if not quick_env_setup(verbose=True):
    print("✗ Environment setup failed")
    exit(1)
```

### Advanced Configuration

For more control, use the advanced configuration system:

```python
from helpers.env_config import load_environment_config, get_env_config

# Load and validate environment
success, errors = load_environment_config()
if not success:
    for error in errors:
        print(f"Error: {error}")
    exit(1)

# Get configuration instance
env_config = get_env_config()

# Display configuration
env_config.display_configuration()

# Get values
jwt_secret = env_config.get('JWT_SECRET')
db_pool_size = env_config.get_int('DB_POOL_SIZE', 5)
debug_mode = env_config.get_bool('DEBUG', False)
```

### Basic Environment Loading

For simple scripts that just need .env loading:

```python
from helpers.env_loader import load_dotenv_with_fallback

# Load environment variables
success, errors = load_dotenv_with_fallback(verbose=True)
if not success:
    print("Failed to load environment")
    exit(1)
```

## Environment Variables

### Required Variables

#### JWT Configuration
- `JWT_SECRET` - JWT signing secret (min 8 chars dev, 32 chars production)

#### Database Configuration (based on DB_TYPE)

**PostgreSQL** (`DB_TYPE=postgres`):
- `POSTGRES_USER` - Database username
- `POSTGRES_PASS` - Database password (can be encrypted)
- `POSTGRES_HOST` - Database host
- `POSTGRES_PORT` - Database port (default: 5432)
- `POSTGRES_DB` - Database name

**MSSQL** (`DB_TYPE=mssql`):
- `MSSQL_USER` - Database username
- `MSSQL_PASS` - Database password (can be encrypted)
- `MSSQL_HOST` - Database host
- `MSSQL_DB` - Database name

**Snowflake** (`DB_TYPE=snowflake`):
- `SNOWFLAKE_USER` - Username
- `SNOWFLAKE_PASSWORD` - Password (can be encrypted)
- `SNOWFLAKE_ACCOUNT` - Account identifier
- `SNOWFLAKE_DATABASE` - Database name
- `SNOWFLAKE_SCHEMA` - Schema name

### Optional Variables

#### Application Settings
- `ENVIRONMENT` - Environment type (development, staging, production)
- `JWT_ISSUER` - JWT token issuer (default: csi-adapter)
- `JWT_AUDIENCE` - JWT token audience (default: csi-knowledgequest)

#### Database Settings
- `DB_TYPE` - Database type (default: postgres)
- `DB_POOL_SIZE` - Connection pool size (default: 5)
- `DB_MAX_OVERFLOW` - Max overflow connections (default: 10)
- `DB_POOL_TIMEOUT` - Pool timeout in seconds (default: 30)
- `DB_POOL_RECYCLE` - Connection recycle time (default: 3600)
- `DB_CONNECT_TIMEOUT` - Connection timeout (default: 10)
- `DB_COMMAND_TIMEOUT` - Command timeout (default: 30)

#### PostgreSQL Specific
- `POSTGRES_SCHEMA` - Schema name (default: public)

#### MSSQL Specific
- `MSSQL_DRIVER` - ODBC driver (default: ODBC+Driver+18+for+SQL+Server)

#### Snowflake Specific
- `SNOWFLAKE_WAREHOUSE` - Warehouse name (optional)
- `SNOWFLAKE_ROLE` - Role name (optional)

#### Gunicorn Settings
- `GUNICORN_WORKERS` - Number of worker processes
- `GUNICORN_TIMEOUT` - Worker timeout (default: 120)
- `GUNICORN_LOG_LEVEL` - Log level (default: info)

## Environment-Specific Configuration

### Development Environment

Create `.env.development`:
```bash
ENVIRONMENT=development
JWT_SECRET=dev-secret-key
DB_TYPE=postgres
# ... other development-specific settings
```

### Staging Environment

Create `.env.staging`:
```bash
ENVIRONMENT=staging
JWT_SECRET=staging-secret-key-32-characters-long
DB_TYPE=postgres
# ... staging-specific settings
```

### Production Environment

Create `.env.production`:
```bash
ENVIRONMENT=production
JWT_SECRET=production-secret-key-must-be-32-characters-or-longer
DB_TYPE=postgres
# ... production settings with stricter validation
```

## Validation Rules

### Environment-Specific Validation

- **Development**: Relaxed validation (JWT_SECRET min 8 chars)
- **Staging**: Moderate validation
- **Production**: Strict validation (JWT_SECRET min 32 chars)

### Type Validation

- **Integers**: Database pool sizes, timeouts, ports
- **Booleans**: Debug flags, feature toggles
- **Enums**: Database types, log levels, environments

### Security

- Automatic masking of sensitive variables in logs
- Variables containing `SECRET`, `PASS`, `TOKEN`, `KEY` are masked
- Validation without exposing actual values

## Error Handling

### Common Error Messages

1. **Missing Required Variables**:
   ```
   JWT_SECRET: MISSING - JWT signing secret key
   ```

2. **Validation Failures**:
   ```
   JWT_SECRET must be at least 32 characters long in production
   ```

3. **Type Errors**:
   ```
   DB_POOL_SIZE must be a positive integer, got: abc
   ```

4. **Environment File Issues**:
   ```
   No .env file found, using system environment variables only
   ```

## Migration Guide

### From Old System

Replace old environment loading:

```python
# OLD
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✓ Loaded environment variables from .env file")
except ImportError:
    print("⚠ python-dotenv not installed")
except Exception as e:
    print(f"⚠ Could not load .env file: {e}")
```

With new system:

```python
# NEW
from helpers.env_loader import quick_env_setup

if not quick_env_setup(verbose=True):
    print("✗ Environment setup failed")
    exit(1)
```

### Benefits of Migration

1. **Standardized Loading**: Consistent across all scripts
2. **Better Error Handling**: Clear error messages and validation
3. **Environment Support**: Automatic environment-specific configuration
4. **Security**: Automatic masking of sensitive values
5. **Validation**: Type checking and business rule validation

## Best Practices

1. **Use Quick Setup**: For most applications, `quick_env_setup()` is sufficient
2. **Environment Files**: Use environment-specific files for different deployments
3. **Validation**: Let the system validate your configuration automatically
4. **Security**: Never commit actual secrets to version control
5. **Documentation**: Document any custom environment variables you add

## Troubleshooting

### Environment Not Loading

1. Check file exists: `.env`, `.env.development`, etc.
2. Verify file permissions
3. Check for syntax errors in .env file
4. Ensure `python-dotenv` is installed

### Validation Failures

1. Check variable names match exactly
2. Verify required variables are set
3. Check data types (integers, booleans)
4. Review environment-specific requirements

### Database Connection Issues

1. Verify database-specific variables are set
2. Check `DB_TYPE` matches your database
3. Validate connection parameters
4. Test database connectivity separately
