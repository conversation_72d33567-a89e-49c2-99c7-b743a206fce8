# Database Setup Guide

## Overview

The Knowledge Quest Receiver application requires a PostgreSQL database with specific tables to store analysis data. This guide explains how to set up and initialize the database schema.

## Required Tables

The application uses three main tables:

1. **`kq_analysis`** - Main analysis records
2. **`kq_analysis_taxonomy`** - Taxonomy classifications for analyses
3. **`kq_analysis_question`** - Questions and answers associated with analyses

## Database Configuration

### Environment Variables

Ensure the following environment variables are set in your `.env` file:

```bash
# Database Configuration
DB_TYPE=postgres
POSTGRES_USER=your_username
POSTGRES_PASS=your_password  # Can be encrypted with enc:v2: prefix
POSTGRES_HOST=your_host
POSTGRES_PORT=5432
POSTGRES_DB=your_database_name
POSTGRES_SCHEMA=contactcentredb  # Schema where tables will be created
```

### Schema Requirements

- The application expects tables to be in the `contactcentredb` schema (or as specified by `POSTGRES_SCHEMA`)
- The schema must exist before running the initialization script
- The database user must have CREATE, SELECT, INSERT, UPDATE, DELETE permissions on the schema

## Database Initialization

### Option 1: Automatic Initialization (Recommended)

Run the database initialization script:

```bash
python init_database.py
```

This script will:
- Test database connectivity
- Check if the required schema exists
- Create the schema if needed (for non-public schemas)
- Check if tables already exist
- Create all required tables using SQLAlchemy models
- Verify table creation

### Option 2: Manual Table Creation

If you prefer to create tables manually, you can use the SQLAlchemy models:

```python
from helpers.db import engine
from helpers.db_models import Base

# Create all tables
Base.metadata.create_all(bind=engine)
```

## Database Status Checking

To check the current database status without making changes:

```bash
python check_database.py
```

This will show:
- Database connection status
- Schema existence
- Table existence and row counts
- Table structure details

## Troubleshooting

### Common Issues

1. **"Database schema not initialized - required tables are missing"**
   - Solution: Run `python init_database.py`

2. **"Schema 'contactcentredb' does not exist"**
   - Solution: Create the schema manually or ensure your database user has schema creation permissions

3. **Connection errors**
   - Check your database credentials in `.env`
   - Ensure the database server is accessible
   - Verify firewall settings

### Error Messages

The application provides specific error messages for database issues:

- **HTTP 503**: "Service temporarily unavailable - database schema not initialized"
  - Indicates missing tables, run initialization script
  
- **HTTP 503**: "Service temporarily unavailable - database connection error"
  - Indicates connectivity issues, check database server and credentials

- **HTTP 500**: "Database operation failed"
  - Indicates application-level database errors, check logs for details

## Table Structure

### kq_analysis
- `kq_analysisid` (VARCHAR, Primary Key)
- `conversationid` (VARCHAR, NOT NULL)
- `communicationid` (VARCHAR, NOT NULL)
- `callsummary` (TEXT, NULL)
- `callresolution` (VARCHAR, NULL)
- `callbackrequired` (BOOLEAN, NULL)
- `selfserviceattempted` (BOOLEAN, NULL)
- `selfserviceproblems` (BOOLEAN, NULL)
- `satisfactionsentiment` (NUMERIC, NULL)

### kq_analysis_taxonomy
- `kq_analysistaxonomyid` (VARCHAR, Primary Key)
- `kq_analysisid` (VARCHAR, Foreign Key to kq_analysis)
- `taxonomy` (VARCHAR, NULL)

### kq_analysis_question
- `kq_analysisquestionid` (VARCHAR, Primary Key)
- `kq_analysisid` (VARCHAR, Foreign Key to kq_analysis)
- `question` (TEXT, NULL)
- `answer` (TEXT, NULL)
- `taxonomy` (VARCHAR, NULL)
- `knowledgeid` (VARCHAR, NULL)
- `knowledge_confidence` (NUMERIC, NULL)

## Testing

After initialization, you can test the database setup:

```bash
python test_schema_fix.py
```

This will perform comprehensive tests including:
- Database connectivity
- Table queries
- Record insertion
- Record retrieval
- Related table operations

## Maintenance

### Regular Checks

- Monitor database connection pool usage via `/metrics` endpoint
- Check table row counts periodically
- Monitor database performance and query times

### Backup Considerations

Ensure regular backups of:
- All three main tables
- The `contactcentredb` schema
- Any custom indexes or constraints

## Production Deployment

For production deployments:

1. Run `python check_database.py` to verify setup
2. Run `python init_database.py` if tables are missing
3. Test with `python test_schema_fix.py`
4. Monitor application logs for database-related errors
5. Set up database monitoring and alerting
