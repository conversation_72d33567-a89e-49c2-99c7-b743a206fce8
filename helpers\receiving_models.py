from pydantic import BaseModel, Field
from typing import List, Optional
from decimal import Decimal

class AnalysisTaxonomy(BaseModel):
    kq_analysistaxonomyid: str
    taxonomy: Optional[str] = None

class AnalysisQuestion(BaseModel):
    kq_analysisquestionid: str
    question: Optional[str] = None
    answer: Optional[str] = None
    taxonomy: Optional[str] = None
    knowledgeid: Optional[str] = None
    knowledge_confidence: Optional[Decimal] = None

class Analysis(BaseModel):
    kq_analysisid: str
    conversationid: str
    communicationid: str
    call_summary: Optional[str] = None
    call_resolution: Optional[str] = None
    callback_required: Optional[bool] = None
    selfservice_attempted: Optional[bool] = None
    selfservice_problems: Optional[bool] = None
    satisfaction_sentiment: Optional[Decimal] = None
    taxonomies: List[AnalysisTaxonomy] = Field(default_factory=list)
    questions: List[AnalysisQuestion] = Field(default_factory=list)