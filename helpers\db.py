import os
import logging
from contextlib import contextmanager
from typing import Literal, Iterator

from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.exc import ArgumentError, OperationalError
from sqlalchemy.orm import sessionmaker, Session



log = logging.getLogger(__name__)

class ConfigError(Exception):
    """Raised when database configuration is invalid."""

def _get_env_var(
    name: str,
    *,
    required: bool = True,
    default: str = ""
) -> str:
    val = os.getenv(name, default)
    if required and not val:
        raise ConfigError(f"Environment variable {name!r} is required and must not be empty.")
    return val

def get_connection_string(
    db_type: Literal["mssql", "postgres", "snowflake"]
) -> str:
    t = db_type.lower()
    if t == "mssql":
        user     = _get_env_var("MSSQL_USER")
        password = _get_env_var("MSSQL_PASS")
        host     = _get_env_var("MSSQL_HOST")
        database = _get_env_var("MSSQL_DB")
        driver   = _get_env_var("MSSQL_DRIVER", required=False, default="ODBC+Driver+18+for+SQL+Server")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting:", result)
            raise ConfigError(f"Password unable to be decrypted")
            
        return (
            f"mssql+pyodbc://{user}:{password}@{host}/{database}"
            f"?driver={driver}&LongAsMax=yes"
        )

    if t == "postgres":
        user     = _get_env_var("POSTGRES_USER")
        password = _get_env_var("POSTGRES_PASS")
        host     = _get_env_var("POSTGRES_HOST")
        port     = _get_env_var("POSTGRES_PORT")
        database = _get_env_var("POSTGRES_DB")
        schema   = _get_env_var("POSTGRES_SCHEMA", required=False, default="public")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting: %s", result)
            raise ConfigError(f"Password unable to be decrypted")

        # Build connection string with schema support
        conn_string = f"postgresql+psycopg2://{user}:{password}@{host}:{port}/{database}"
        if schema and schema != "public":
            conn_string += f"?options=-csearch_path%3D{schema}"
        return conn_string

    if t == "snowflake":
        user      = _get_env_var("SNOWFLAKE_USER")
        password  = _get_env_var("SNOWFLAKE_PASSWORD")
        account   = _get_env_var("SNOWFLAKE_ACCOUNT")
        database  = _get_env_var("SNOWFLAKE_DATABASE")
        schema    = _get_env_var("SNOWFLAKE_SCHEMA")
        warehouse = _get_env_var("SNOWFLAKE_WAREHOUSE", required=False, default="")
        role      = _get_env_var("SNOWFLAKE_ROLE", required=False, default="")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting:", result)
            raise ConfigError(f"Password unable to be decrypted")


        params: list[str] = []
        if warehouse:
            params.append(f"warehouse={warehouse}")
        if role:
            params.append(f"role={role}")
        qs = "&".join(params)
        base = f"snowflake+snowflakeconnector://{user}:{password}@{account}/{database}/{schema}"
        return f"{base}?{qs}" if qs else base

    raise ConfigError(f"Unsupported DB_TYPE {db_type!r}. Must be one of: mssql, postgres, snowflake.")

# load & validate config
DB_TYPE: Literal["mssql", "postgres", "snowflake"] = os.getenv("DB_TYPE", "mssql")  # type: ignore[assignment]
try:
    CONN_STRING = get_connection_string(DB_TYPE)

    # Base engine configuration with connection pooling and timeouts
    engine_args = {
        'echo': False,
        'pool_size': int(os.getenv('DB_POOL_SIZE', '5')),
        'max_overflow': int(os.getenv('DB_MAX_OVERFLOW', '10')),
        'pool_timeout': int(os.getenv('DB_POOL_TIMEOUT', '30')),
        'pool_recycle': int(os.getenv('DB_POOL_RECYCLE', '3600')),
        'pool_pre_ping': True,  # Verify connections before use
    }

    # Database-specific configurations
    if DB_TYPE == "mssql":
        engine_args['fast_executemany'] = True
        engine_args['connect_args'] = {
            'timeout': int(os.getenv('DB_CONNECT_TIMEOUT', '10')),
            'command_timeout': int(os.getenv('DB_COMMAND_TIMEOUT', '30')),
        }
    elif DB_TYPE == "postgres":
        engine_args['connect_args'] = {
            'connect_timeout': int(os.getenv('DB_CONNECT_TIMEOUT', '10')),
            # PostgreSQL (psycopg2) doesn't support 'command_timeout' parameter
            # Use statement_timeout via options instead for query timeouts
            'options': f"-c statement_timeout={int(os.getenv('DB_COMMAND_TIMEOUT', '30'))}s"
        }
    elif DB_TYPE == "snowflake":
        engine_args['connect_args'] = {
            'network_timeout': int(os.getenv('DB_CONNECT_TIMEOUT', '10')),
            'login_timeout': int(os.getenv('DB_COMMAND_TIMEOUT', '30')),
        }

    log.info(f"Configuring database engine for {DB_TYPE} with pool_size={engine_args['pool_size']}, "
             f"max_overflow={engine_args['max_overflow']}, pool_timeout={engine_args['pool_timeout']}s")

    engine: Engine = create_engine(
        CONN_STRING,
        **engine_args
    )

    log.info("Database engine configured successfully")

except (ConfigError, ArgumentError, OperationalError) as e:
    log.exception("Failed to configure database engine")
    raise

SessionLocal: sessionmaker[Session] = sessionmaker(
    bind=engine,
    autoflush=False,
    autocommit=False,
    expire_on_commit=False,
)

@contextmanager
def session_scope() -> Iterator[Session]:
    """
    Provide a transactional scope around a series of operations.
    Commits on success, rolls back and re-raises on exception, always closes.
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        log.exception("Session rollback because of exception")
        session.rollback()
        raise
    finally:
        session.close()