import os
import logging
from contextlib import contextmanager
from typing import Literal, Iterator

from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.exc import ArgumentError, OperationalError
from sqlalchemy.orm import sessionmaker, Session

from helpers.simple3des import decrypt_password

log = logging.getLogger(__name__)

class ConfigError(Exception):
    """Raised when database configuration is invalid."""

def _get_env_var(
    name: str,
    *,
    required: bool = True,
    default: str = ""
) -> str:
    val = os.getenv(name, default)
    if required and not val:
        raise ConfigError(f"Environment variable {name!r} is required and must not be empty.")
    return val

def get_connection_string(
    db_type: Literal["mssql", "postgres", "snowflake"]
) -> str:
    t = db_type.lower()
    if t == "mssql":
        user     = _get_env_var("MSSQL_USER")
        password = _get_env_var("MSSQL_PASS")
        host     = _get_env_var("MSSQL_HOST")
        database = _get_env_var("MSSQL_DB")
        driver   = _get_env_var("MSSQL_DRIVER", required=False, default="ODBC+Driver+18+for+SQL+Server")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting:", result)
            raise ConfigError(f"Password unable to be decrypted")
            
        return (
            f"mssql+pyodbc://{user}:{password}@{host}/{database}"
            f"?driver={driver}&LongAsMax=yes"
        )

    if t == "postgres":
        user     = _get_env_var("POSTGRES_USER")
        password = _get_env_var("POSTGRES_PASS")
        host     = _get_env_var("POSTGRES_HOST")
        port     = _get_env_var("POSTGRES_PORT")
        database = _get_env_var("POSTGRES_DB")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting:", result)
            raise ConfigError(f"Password unable to be decrypted")

        return f"postgresql+psycopg2://{user}:{password}@{host}:{port}/{database}"

    if t == "snowflake":
        user      = _get_env_var("SNOWFLAKE_USER")
        password  = _get_env_var("SNOWFLAKE_PASSWORD")
        account   = _get_env_var("SNOWFLAKE_ACCOUNT")
        database  = _get_env_var("SNOWFLAKE_DATABASE")
        schema    = _get_env_var("SNOWFLAKE_SCHEMA")
        warehouse = _get_env_var("SNOWFLAKE_WAREHOUSE", required=False, default="")
        role      = _get_env_var("SNOWFLAKE_ROLE", required=False, default="")
        # Decrypt password
        success, result = decrypt_password(user, password)
        if success:
            password = result
        else:
            log.warning("Error decrypting:", result)
            raise ConfigError(f"Password unable to be decrypted")


        params: list[str] = []
        if warehouse:
            params.append(f"warehouse={warehouse}")
        if role:
            params.append(f"role={role}")
        qs = "&".join(params)
        base = f"snowflake+snowflakeconnector://{user}:{password}@{account}/{database}/{schema}"
        return f"{base}?{qs}" if qs else base

    raise ConfigError(f"Unsupported DB_TYPE {db_type!r}. Must be one of: mssql, postgres, snowflake.")

# load & validate config
DB_TYPE: Literal["mssql", "postgres", "snowflake"] = os.getenv("DB_TYPE", "mssql")  # type: ignore[assignment]
try:
    CONN_STRING = get_connection_string(DB_TYPE)
    engine_args = {
        'echo': False
    }
    if DB_TYPE == "mssql":
        engine_args['fast_executemany'] = True
    engine: Engine = create_engine(
        CONN_STRING,
        **engine_args
    )
except (ConfigError, ArgumentError, OperationalError) as e:
    log.exception("Failed to configure database engine")
    raise

SessionLocal: sessionmaker[Session] = sessionmaker(
    bind=engine,
    autoflush=False,
    autocommit=False,
    expire_on_commit=False,
)

@contextmanager
def session_scope() -> Iterator[Session]:
    """
    Provide a transactional scope around a series of operations.
    Commits on success, rolls back and re-raises on exception, always closes.
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception:
        log.exception("Session rollback because of exception")
        session.rollback()
        raise
    finally:
        session.close()