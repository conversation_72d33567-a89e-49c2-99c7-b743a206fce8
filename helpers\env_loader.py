#!/usr/bin/env python3
"""
Simplified environment loader for scripts that need basic .env loading functionality.
This provides a standardized way to load environment variables across all scripts.
"""

import os
import logging
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)


def load_dotenv_with_fallback(env_file: Optional[str] = None, verbose: bool = True) -> Tuple[bool, List[str]]:
    """
    Load environment variables from .env file with comprehensive fallback handling.
    
    Args:
        env_file: Optional specific .env file path
        verbose: Whether to print status messages
        
    Returns:
        Tuple of (success, error_messages)
    """
    errors = []
    
    try:
        from dotenv import load_dotenv
        
        # Determine which .env files to try
        env_files_to_try = []
        
        if env_file:
            env_files_to_try.append(env_file)
        else:
            # Try environment-specific files first
            env_type = os.getenv('ENVIRONMENT', 'development')
            env_files_to_try.extend([
                f'.env.{env_type}',
                f'.env.{env_type}.local',
                '.env.local',
                '.env'
            ])
        
        # Try to load the first available file
        loaded_file = None
        for env_path in env_files_to_try:
            if os.path.exists(env_path):
                try:
                    load_dotenv(env_path, override=True)
                    loaded_file = env_path
                    if verbose:
                        print(f"✓ Loaded environment variables from {env_path}")
                    logger.info(f"Loaded environment variables from {env_path}")
                    break
                except Exception as e:
                    error_msg = f"Failed to load {env_path}: {e}"
                    errors.append(error_msg)
                    if verbose:
                        print(f"⚠ {error_msg}")
                    continue
        
        if not loaded_file:
            if env_file:
                error_msg = f"Specified .env file not found: {env_file}"
                errors.append(error_msg)
                if verbose:
                    print(f"X {error_msg}")
                return False, errors
            else:
                warning_msg = "No .env file found, using system environment variables only"
                if verbose:
                    print(f"⚠ {warning_msg}")
                logger.warning(warning_msg)
                # This is not an error - system env vars might be sufficient
        
        return True, errors
        
    except ImportError:
        error_msg = "python-dotenv not installed, .env file will not be loaded automatically"
        if verbose:
            print(f"⚠ {error_msg}")
        logger.warning(error_msg)
        errors.append(error_msg)
        # This is not a failure - we can still use system environment variables
        return True, errors
        
    except Exception as e:
        error_msg = f"Could not load .env file: {e}"
        if verbose:
            print(f"✗ {error_msg}")
        logger.error(error_msg)
        errors.append(error_msg)
        return False, errors


def validate_required_env_vars(required_vars: List[str], verbose: bool = True) -> Tuple[bool, List[str]]:
    """
    Validate that required environment variables are present.
    
    Args:
        required_vars: List of required environment variable names
        verbose: Whether to print status messages
        
    Returns:
        Tuple of (success, missing_variables)
    """
    missing_vars = []
    
    for var_name in required_vars:
        value = os.getenv(var_name)
        if not value:
            missing_vars.append(var_name)
            if verbose:
                print(f"✗ Missing required environment variable: {var_name}")
        else:
            if verbose:
                # Mask sensitive values
                display_value = "***MASKED***" if any(keyword in var_name.upper() 
                                                    for keyword in ['SECRET', 'PASS', 'TOKEN', 'KEY']) else value
                print(f"✓ {var_name}: {display_value}")
    
    success = len(missing_vars) == 0
    return success, missing_vars


def setup_environment(required_vars: Optional[List[str]] = None, 
                     env_file: Optional[str] = None, 
                     verbose: bool = True) -> bool:
    """
    Complete environment setup with loading and validation.
    
    Args:
        required_vars: List of required environment variable names
        env_file: Optional specific .env file path
        verbose: Whether to print status messages
        
    Returns:
        True if setup successful, False otherwise
    """
    # Load environment variables
    load_success, load_errors = load_dotenv_with_fallback(env_file, verbose)
    
    if not load_success:
        if verbose:
            print("Environment loading failed:")
            for error in load_errors:
                print(f"  - {error}")
        return False
    
    # Validate required variables if specified
    if required_vars:
        validate_success, missing_vars = validate_required_env_vars(required_vars, verbose)
        
        if not validate_success:
            if verbose:
                print(f"Environment validation failed - missing variables: {', '.join(missing_vars)}")
            return False
    
    if verbose and load_errors:
        print("Environment loaded with warnings:")
        for error in load_errors:
            print(f"  - {error}")
    
    return True


def get_database_specific_vars(db_type: Optional[str] = None) -> List[str]:
    """
    Get the list of required environment variables for a specific database type.
    
    Args:
        db_type: Database type (postgres, mssql, snowflake). If None, uses DB_TYPE env var.
        
    Returns:
        List of required environment variable names
    """
    if db_type is None:
        db_type = os.getenv('DB_TYPE', 'postgres').lower()
    
    base_vars = ['JWT_SECRET']
    
    if db_type == 'postgres':
        return base_vars + [
            'POSTGRES_USER', 'POSTGRES_PASS', 'POSTGRES_HOST', 
            'POSTGRES_PORT', 'POSTGRES_DB'
        ]
    elif db_type == 'mssql':
        return base_vars + [
            'MSSQL_USER', 'MSSQL_PASS', 'MSSQL_HOST', 'MSSQL_DB'
        ]
    elif db_type == 'snowflake':
        return base_vars + [
            'SNOWFLAKE_USER', 'SNOWFLAKE_PASSWORD', 'SNOWFLAKE_ACCOUNT',
            'SNOWFLAKE_DATABASE', 'SNOWFLAKE_SCHEMA'
        ]
    else:
        logger.warning(f"Unknown database type: {db_type}")
        return base_vars


def quick_env_setup(verbose: bool = True) -> bool:
    """
    Quick environment setup for database applications.
    Loads .env file and validates database-specific variables.
    
    Args:
        verbose: Whether to print status messages
        
    Returns:
        True if setup successful, False otherwise
    """
    # Load environment first to get DB_TYPE
    load_success, _ = load_dotenv_with_fallback(verbose=verbose)
    if not load_success:
        return False
    
    # Get required variables based on database type
    required_vars = get_database_specific_vars()
    
    # Validate required variables
    validate_success, _ = validate_required_env_vars(required_vars, verbose)
    
    return validate_success


# Convenience function for backward compatibility
def load_env_with_feedback(verbose: bool = True) -> bool:
    """
    Simple environment loading with user feedback.
    Maintains backward compatibility with existing scripts.
    
    Args:
        verbose: Whether to print status messages
        
    Returns:
        True if loading successful, False otherwise
    """
    success, _ = load_dotenv_with_fallback(verbose=verbose)
    return success
