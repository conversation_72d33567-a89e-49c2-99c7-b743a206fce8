
# CSI KnowledgeQuest Adapter

This micro-service receives KnowledgeQuest **analysis** payloads, validates them, and persists them to a relational database.  
It is designed to be deployed as a container behind an API-gateway that provides or forwards a JWT.

## Features

* Flask 3 based JSON API (single `POST /` endpoint)  
* Pydantic v2 input validation  
* JWT (HS-256) verification with issuer & audience checks  
* SQLAlchemy 2 ORM with pluggable back-ends  
  * Microsoft SQL Server  
  * PostgreSQL  
  * Snowflake  
* First-class Dockerfile and gunicorn entry-point  
* Opinionated logging configuration (PID, timestamp, module, line, level, …)

---

## Quick start (Docker)

```bash
# 1. Build the image
docker build -t kq-adapter .

# 2. Run the container – example using PostgreSQL
docker run -p 8080:8080 \
  -e JWT_SECRET=supersecret \
  -e DB_TYPE=postgres \
  -e POSTGRES_USER=app \
  -e POSTGRES_PASS=app_pass \
  -e POSTGRES_HOST=db.example.com \
  -e POSTGRES_PORT=5432 \
  -e POSTGRES_DB=knowledge \
  kq-adapter
```

The service will listen on `http://0.0.0.0:8080`.

---

## API

### `POST /`

Consumes a JSON body matching the schema below and **must** include the header

```
Authorization: Bearer <JWT>
```

#### Example request

```http
POST / HTTP/1.1
Host: adapter.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "kq_analysisid": "5953db7c-fadd-4e66-aa75-b2fafc15d8ab",
  "conversationid": "99511f29-8948-40d0-8b00-17c75149305d",
  "communicationid": "2e8fab0b-20bf-41ff-9302-9333e6dc3744",
  "call_summary": "Customer called about invoice #123",
  "taxonomies": [
    {
      "kq_analysistaxonomyid": "cc1e5c0d-17d3-4048-a7c8-18adb6c5f185",
      "taxonomy": "billing"
    }
  ],
  "questions": [
    {
      "kq_analysisquestionid": "a198deca-e770-4cd1-9f76-0403e708f4ef",
      "question": "How do I download my invoice?",
      "answer": "From the billing portal...",
      "taxonomy": "billing"
    }
  ]
}
```

* 201 Created – new analysis inserted  
* 200 OK – analysis already existed (idempotent)  
* 401 – missing / invalid JWT  
* 422 – payload failed validation  

---

## JWT requirements

* Signed with HS-256
* Must contain:
  * `iss` = `$JWT_ISSUER` (default: `csi-adapter`)
  * `aud` = `$JWT_AUDIENCE` (default: `csi-knowledgequest`)
* Verified using the secret in `$JWT_SECRET` (required)

---

## Environment variables

General  
* `JWT_SECRET` (required)  
* `JWT_ISSUER` (optional, default `csi-adapter`)  
* `JWT_AUDIENCE` (optional, default `csi-knowledgequest`)  

Database selection  
* `DB_TYPE` = `mssql` | `postgres` | `snowflake` (default `mssql`)

Depending on `DB_TYPE` one of the following groups is required:

MSSQL  
```
MSSQL_USER, MSSQL_PASS, MSSQL_HOST, MSSQL_DB
# optional
MSSQL_DRIVER            # default: ODBC Driver 18 for SQL Server
```

PostgreSQL  
```
POSTGRES_USER, POSTGRES_PASS, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB
```

Snowflake  
```
SNOWFLAKE_USER, SNOWFLAKE_PASSWORD, SNOWFLAKE_ACCOUNT,
SNOWFLAKE_DATABASE, SNOWFLAKE_SCHEMA
# optional
SNOWFLAKE_WAREHOUSE, SNOWFLAKE_ROLE
```

---

## Local development

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
export JWT_SECRET=dev
export DB_TYPE=postgres ...
python -m main
```

---

## Database schema

SQLAlchemy models are defined in `db_models.py` and will generate / expect
the following tables (names simplified):

* `kq_analysis`
* `kq_analysis_taxonomy`
* `kq_analysis_question`

The FK `kq_analysis_question.knowledgeid` is optional and points to an
external `knowledge` table if present.

---

## Testing

Unit tests are not included yet.  A suggested stack is:

* pytest + pytest-cov
* moto or similar for mocking any cloud services
* test-containers for throwing away a real Postgres or MSSQL instance

---

## Deploying to production

Set all secrets using your orchestration platform of choice and point an
ingress / load balancer to port 8080.  
Gunicorn is the default WSGI server (`CMD` in Dockerfile).  If you need
async you can swap it for `uvicorn` easily.
