#!/usr/bin/env python3
"""
Test script to verify schema validation behavior when schema doesn't exist.
"""

import os
import sys
import subprocess

# Load environment variables
from helpers.env_loader import quick_env_setup

if not quick_env_setup(verbose=False):
    print("✗ Environment setup failed")
    exit(1)

def test_schema_validation():
    """Test schema validation with non-existent schema."""
    print("=" * 60)
    print("TESTING SCHEMA VALIDATION BEHAVIOR")
    print("=" * 60)
    
    # Save original schema
    original_schema = os.getenv('POSTGRES_SCHEMA', 'public')
    print(f"Original schema: {original_schema}")
    
    # Test with non-existent schema
    test_schema = "nonexistent_schema_test"
    print(f"Testing with non-existent schema: {test_schema}")
    
    # Set environment variable for test
    os.environ['POSTGRES_SCHEMA'] = test_schema
    
    try:
        # Import and test the validation function directly
        from init_database import validate_schema_exists
        
        print("\nTesting validate_schema_exists() function...")
        result = validate_schema_exists()
        
        if result:
            print("✗ UNEXPECTED: Function returned True for non-existent schema")
            return False
        else:
            print("✓ EXPECTED: Function correctly returned False for non-existent schema")
            
        # Restore original schema
        os.environ['POSTGRES_SCHEMA'] = original_schema
        
        print(f"\nTesting with existing schema: {original_schema}")
        result = validate_schema_exists()
        
        if result:
            print("✓ EXPECTED: Function correctly returned True for existing schema")
            return True
        else:
            print("✗ UNEXPECTED: Function returned False for existing schema")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        return False
    finally:
        # Always restore original schema
        os.environ['POSTGRES_SCHEMA'] = original_schema

def test_init_database_failure():
    """Test that init_database.py fails when schema doesn't exist."""
    print("\n" + "=" * 60)
    print("TESTING INIT_DATABASE.PY FAILURE BEHAVIOR")
    print("=" * 60)
    
    # Save original schema
    original_schema = os.getenv('POSTGRES_SCHEMA', 'public')
    
    try:
        # Create a temporary environment with non-existent schema
        env = os.environ.copy()
        env['POSTGRES_SCHEMA'] = 'nonexistent_schema_test'
        
        print("Running init_database.py with non-existent schema...")
        
        # Run init_database.py as subprocess with modified environment
        result = subprocess.run(
            [sys.executable, 'init_database.py'],
            env=env,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        print(f"Return code: {result.returncode}")
        
        if result.returncode != 0:
            print("✓ EXPECTED: init_database.py correctly failed with non-existent schema")
            print("Error output:")
            for line in result.stderr.split('\n')[-10:]:  # Show last 10 lines
                if line.strip():
                    print(f"  {line}")
            return True
        else:
            print("✗ UNEXPECTED: init_database.py succeeded with non-existent schema")
            print("Output:")
            print(result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Test timed out")
        return False
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        return False

def main():
    """Run all schema validation tests."""
    print("SCHEMA VALIDATION TESTING")
    print("=" * 80)
    
    tests = [
        ("Schema Validation Function", test_schema_validation),
        ("Init Database Failure", test_init_database_failure),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 80)
    print("TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{status:4} - {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        print("❌ SOME TESTS FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
