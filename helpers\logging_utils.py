import logging
import uuid
import time
import psutil
import os
from contextvars import Context<PERSON><PERSON>
from functools import wraps
from typing import Optional, Dict, Any
from flask import request, g

# Context variable to store correlation ID across request lifecycle
correlation_id: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)

class StructuredFormatter(logging.Formatter):
    """Custom formatter that adds structured fields to log records."""
    
    def format(self, record):
        # Add correlation ID if available
        corr_id = correlation_id.get()
        if corr_id:
            record.correlation_id = corr_id
        else:
            record.correlation_id = "N/A"
        
        # Add memory usage
        try:
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            record.memory_mb = f"{memory_mb:.1f}"
        except:
            record.memory_mb = "N/A"
        
        return super().format(record)

def setup_structured_logging():
    """Configure structured logging for the application."""
    formatter = StructuredFormatter(
        '%(process)d:%(asctime)s - %(correlation_id)s - %(memory_mb)sMB - '
        '%(module)s:%(name)s - %(filename)s:%(lineno)s - '
        '%(funcName)20s() - %(levelname)s - %(message)s'
    )

    # Clear existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # Set up console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(formatter)

    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG)

    # Suppress verbose SQLAlchemy and psycopg2 logging in production
    # Keep them at WARNING level to reduce noise while preserving important errors
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.dialects').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.orm').setLevel(logging.WARNING)
    logging.getLogger('psycopg2').setLevel(logging.WARNING)

    # Keep our application logging at DEBUG level
    logging.getLogger('main').setLevel(logging.DEBUG)
    logging.getLogger('helpers').setLevel(logging.DEBUG)

    return root_logger

def generate_correlation_id() -> str:
    """Generate a unique correlation ID for request tracking."""
    return str(uuid.uuid4())[:8]

def set_correlation_id(corr_id: str):
    """Set the correlation ID for the current context."""
    correlation_id.set(corr_id)

def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID."""
    return correlation_id.get()

def log_request_timing(func):
    """Decorator to log request timing and memory usage."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = None
        
        try:
            process = psutil.Process(os.getpid())
            start_memory = process.memory_info().rss / 1024 / 1024
        except:
            pass
        
        logger = logging.getLogger(func.__module__)
        corr_id = get_correlation_id()
        
        logger.info(f"Request started - Function: {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # Calculate memory delta if possible
            memory_info = ""
            if start_memory:
                try:
                    end_memory = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
                    memory_delta = end_memory - start_memory
                    memory_info = f" - Memory delta: {memory_delta:+.1f}MB"
                except:
                    pass
            
            logger.info(f"Request completed - Function: {func.__name__} - Duration: {duration:.3f}s{memory_info}")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Request failed - Function: {func.__name__} - Duration: {duration:.3f}s - Error: {str(e)}")
            raise
    
    return wrapper

def log_database_operation(operation_name: str):
    """Decorator to log database operation timing."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            logger = logging.getLogger(func.__module__)
            
            logger.debug(f"DB operation started: {operation_name}")
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"DB operation completed: {operation_name} - Duration: {duration:.3f}s")
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"DB operation failed: {operation_name} - Duration: {duration:.3f}s - Error: {str(e)}")
                raise
        
        return wrapper
    return decorator
