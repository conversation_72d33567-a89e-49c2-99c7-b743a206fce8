"""
Gunicorn configuration for Knowledge Quest Receiver.
Addresses worker timeout issues observed in production logs.
"""
import os
import multiprocessing

# Server socket
bind = "0.0.0.0:8080"
backlog = 2048

# Worker processes
workers = int(os.getenv('GUNICORN_WORKERS', multiprocessing.cpu_count() * 2 + 1))
worker_class = "sync"
worker_connections = 1000
max_requests = int(os.getenv('GUNICORN_MAX_REQUESTS', '1000'))
max_requests_jitter = int(os.getenv('GUNICORN_MAX_REQUESTS_JITTER', '100'))

# Timeout settings - increased from default 30s to handle database operations
timeout = int(os.getenv('GUNICORN_TIMEOUT', '120'))  # 2 minutes
keepalive = int(os.getenv('GUNICORN_KEEPALIVE', '5'))
graceful_timeout = int(os.getenv('GUNICORN_GRACEFUL_TIMEOUT', '30'))

# Memory and performance optimizations
preload_app = True
worker_tmp_dir = "/dev/shm"  # Use memory for worker temp files (if available)

# Logging
loglevel = os.getenv('GUNICORN_LOG_LEVEL', 'info')
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log to stderr
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "kq-receiver"

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Worker lifecycle hooks
def on_starting(server):
    """Called just before the master process is initialized."""
    server.log.info("Starting Knowledge Quest Receiver with %d workers", workers)
    server.log.info("Worker timeout set to %d seconds", timeout)

def on_reload(server):
    """Called to recycle workers during a reload via SIGHUP."""
    server.log.info("Reloading Knowledge Quest Receiver")

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info("Worker %s interrupted", worker.pid)

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    server.log.debug("Worker %s about to be forked", worker.pid)

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker %s spawned", worker.pid)

def pre_exec(server):
    """Called just before a new master process is forked."""
    server.log.info("Forked child, re-executing.")

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("Server is ready. Spawning workers")

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.warning("Worker %s aborted", worker.pid)

def pre_request(worker, req):
    """Called just before a worker processes the request."""
    worker.log.debug("Processing request: %s %s", req.method, req.path)

def post_request(worker, req, environ, resp):
    """Called after a worker processes the request."""
    worker.log.debug("Completed request: %s %s - Status: %s", 
                     req.method, req.path, resp.status_code)
