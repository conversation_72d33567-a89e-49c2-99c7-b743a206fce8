# Code Cleanup Recommendations

## 🗑️ Files to Remove Completely

### 1. **test.py** - Unused test file
- **Size**: 65 lines
- **Reason**: Contains test data but is never imported or used
- **Action**: `rm test.py`

### 2. **helpers/simple3des.py** - Unused encryption module
- **Size**: 82 lines  
- **Reason**: Imported in helpers/db.py but functions never called
- **Action**: `rm helpers/simple3des.py`

### 3. **helpers/env_config.py** - Unused comprehensive environment config
- **Size**: 564 lines
- **Reason**: Never imported or used anywhere in the codebase
- **Action**: `rm helpers/env_config.py`

## 📦 Dependencies to Remove

### From requirements.txt:
```diff
- pycryptodome>=3.21.0     # Only used in unused simple3des.py
- pyodbc>=5.1.0            # MSSQL driver, app only uses PostgreSQL  
- snowflake-connector-python>=3.7.3  # Snowflake driver, app only uses PostgreSQL
```

## 🔧 Functions to Remove

### From helpers/env_loader.py:
- `validate_required_env_vars()` - Never called
- `setup_environment()` - Never called
- `get_database_specific_vars()` - Never called  
- `quick_env_setup()` - Never called

### From main.py:
- `handle_database_error()` - Defined but never called

### From helpers/db_crud.py imports in main.py:
- `get_kq_analysis_taxonomy_by_id` - Never called
- `get_kq_analysis_question_by_id` - Never called

## 🔄 Code Consolidation Opportunities

### 1. Environment Variable Validation
**Current**: Duplicate validation logic in main.py and helpers/env_loader.py
**Recommendation**: Keep the simple validation in main.py, remove complex unused functions

### 2. Database Connection Testing  
**Current**: Similar logic in check_database.py, init_database.py, and main.py
**Recommendation**: Keep as-is since these are different use cases (utility scripts vs application)

## 📝 Documentation Updates Needed

### 1. DATABASE_SETUP.md
- Remove reference to non-existent `test_schema_fix.py`
- Update examples to reflect actual usage patterns

### 2. ENVIRONMENT_SETUP.md  
- Remove documentation for unused functions from env_config.py
- Focus on actually used functionality

### 3. README.md
- Update to reflect simplified codebase
- Remove references to unused features

## 🎯 Estimated Impact

### Lines of Code Reduction:
- **test.py**: -65 lines
- **helpers/simple3des.py**: -82 lines  
- **helpers/env_config.py**: -564 lines
- **Unused functions in env_loader.py**: ~-150 lines
- **Unused imports/functions in main.py**: ~-20 lines
- **Total**: ~-881 lines (approximately 35% reduction)

### Dependencies Reduction:
- Remove 3 unused packages from requirements.txt
- Reduce Docker image size
- Faster pip install times

### Maintenance Benefits:
- Fewer files to maintain and understand
- Clearer codebase structure  
- Reduced cognitive load for developers
- Faster code reviews

## 🚀 Implementation Plan

### Phase 1: Safe Removals (No Risk)
1. Remove test.py
2. Remove helpers/simple3des.py  
3. Remove helpers/env_config.py
4. Update requirements.txt
5. Remove unused imports from main.py

### Phase 2: Function Cleanup (Low Risk)
1. Remove unused functions from helpers/env_loader.py
2. Remove unused function handle_database_error from main.py
3. Clean up imports in main.py

### Phase 3: Documentation Updates (No Risk)
1. Update DATABASE_SETUP.md
2. Update ENVIRONMENT_SETUP.md  
3. Update README.md

### Phase 4: Testing & Validation
1. Run application to ensure no regressions
2. Test database initialization scripts
3. Verify all endpoints still work
4. Update any CI/CD pipelines if needed

## ⚠️ Files to Keep (Important)

### Utility Scripts (Standalone):
- **check_database.py** - Database status checking utility
- **init_database.py** - Database initialization utility  
- These are meant to be run independently, not imported

### Core Application Files:
- **main.py** - Main Flask application
- **helpers/db.py** - Database connection and configuration
- **helpers/db_crud.py** - Database operations
- **helpers/db_models.py** - SQLAlchemy models
- **helpers/logging_utils.py** - Logging utilities
- **helpers/receiving_models.py** - Pydantic models
- **helpers/env_loader.py** - Environment loading (simplified)

### Configuration Files:
- **gunicorn.conf.py** - Production server configuration
- **Dockerfile** - Container configuration
- **requirements.txt** - Python dependencies (cleaned up)
- **.env** and **env.sample** - Environment configuration

## 🎉 Expected Outcome

After cleanup:
- **~35% fewer lines of code** to maintain
- **Cleaner, more focused codebase**
- **Faster builds and deployments**  
- **Easier onboarding for new developers**
- **Reduced security surface area**
- **Better performance** (fewer imports, smaller memory footprint)
