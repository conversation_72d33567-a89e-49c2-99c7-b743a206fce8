import logging
from typing import Type, TypeVar, Any, Optional
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import DeclarativeMeta, Session
from helpers.db import session_scope
from helpers.logging_utils import log_database_operation

logger = logging.getLogger(__name__)

ModelType = TypeVar("ModelType", bound=DeclarativeMeta)


class DataAccessError(Exception):
    """Schema for wrapping any CRUD‐level failure."""
    def __init__(self, message: str, error_type: str = "general", original_error: Exception = None):
        super().__init__(message)
        self.error_type = error_type
        self.original_error = original_error


def _handle_database_error(e: SQLAlchemyError, operation: str, model_name: str = None) -> DataAccessError:
    """
    Convert SQLAlchemy errors into appropriate DataAccessError with specific error types.
    """
    error_str = str(e)

    # Check for PostgreSQL table/relation not found errors
    if "relation" in error_str and "does not exist" in error_str:
        error_msg = f"Database schema not initialized - required tables are missing"
        logger.error(f"Database schema error during {operation}: {error_msg}")
        return DataAccessError(error_msg, error_type="schema_missing", original_error=e)

    # Check for other common database errors
    elif "connection" in error_str.lower():
        error_msg = f"Database connection error during {operation}"
        logger.error(f"Database connection error: {error_msg}")
        return DataAccessError(error_msg, error_type="connection", original_error=e)

    elif "permission" in error_str.lower() or "access" in error_str.lower():
        error_msg = f"Database permission error during {operation}"
        logger.error(f"Database permission error: {error_msg}")
        return DataAccessError(error_msg, error_type="permission", original_error=e)

    else:
        # Generic database error
        error_msg = f"Database error during {operation}"
        if model_name:
            error_msg += f" on {model_name}"
        logger.error(f"Database error: {error_msg} - {str(e)}")
        return DataAccessError(error_msg, error_type="general", original_error=e)

@log_database_operation("INSERT")
def add_record(model: Type[ModelType], **kwargs: Any) -> ModelType:
    """
    Generic insert. Raises DataAccessError on failure.
    """
    try:
        with session_scope() as session:
            obj = model(**kwargs)
            session.add(obj)
            session.flush()
            logger.debug(f"Successfully added {model.__name__} record")
            return obj
    except SQLAlchemyError as e:
        raise _handle_database_error(e, "INSERT", model.__name__)

@log_database_operation("SELECT")
def get_record(model: Type[ModelType], pk: Any) -> Optional[ModelType]:
    """
    Generic get-by-PK. Raises DataAccessError if not found or on error.
    """
    try:
        with session_scope() as session:
            result = session.get(model, pk)
            logger.debug(f"Fetched {model.__name__} record with id={pk}, found: {result is not None}")
            return result
    except SQLAlchemyError as e:
        raise _handle_database_error(e, "SELECT", model.__name__)

# explicit helpers
from helpers.db_models import KQAnalysis, KQAnalysisTaxonomy, KQAnalysisQuestion

def add_kq_analysis(**kw: Any) -> KQAnalysis:
    return add_record(KQAnalysis, **kw)

def get_kq_analysis_by_id(pk: Any) -> Optional[KQAnalysis]:
    return get_record(KQAnalysis, pk)

def add_kq_analysis_taxonomy(**kw: Any) -> KQAnalysisTaxonomy:
    return add_record(KQAnalysisTaxonomy, **kw)

def get_kq_analysis_taxonomy_by_id(pk: Any) -> Optional[KQAnalysisTaxonomy]:
    return get_record(KQAnalysisTaxonomy, pk)

def add_kq_analysis_question(**kw: Any) -> KQAnalysisQuestion:
    return add_record(KQAnalysisQuestion, **kw)

def get_kq_analysis_question_by_id(pk: Any) -> Optional[KQAnalysisQuestion]:
    return get_record(KQAnalysisQuestion, pk)