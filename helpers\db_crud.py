import logging
from typing import Type, TypeVar, Any, Optional
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import DeclarativeMeta, Session
from helpers.db import session_scope

logger = logging.getLogger(__name__)

ModelType = TypeVar("ModelType", bound=DeclarativeMeta)


class DataAccessError(Exception):
    """Schema for wrapping any CRUD‐level failure."""

def add_record(model: Type[ModelType], **kwargs: Any) -> ModelType:
    """
    Generic insert. Raises DataAccessError on failure.
    """
    try:
        with session_scope() as session:
            obj = model(**kwargs)
            session.add(obj)
            session.flush()
            return obj
    except SQLAlchemyError as e:
        logger.exception("Failed to add %s", model.__name__)
        raise DataAccessError from e

def get_record(model: Type[ModelType], pk: Any) -> Optional[ModelType]:
    """
    Generic get-by-PK. Raises DataAccessError if not found or on error.
    """
    try:
        with session_scope() as session:
            return session.get(model, pk)
    except SQLAlchemyError as e:
        logger.exception("DB error while fetching %s id=%s", model.__name__, pk)
        raise DataAccessError from e

# explicit helpers
from helpers.db_models import KQAnalysis, KQAnalysisTaxonomy, KQAnalysisQuestion

def add_kq_analysis(**kw: Any) -> KQAnalysis:
    return add_record(KQAnalysis, **kw)

def get_kq_analysis_by_id(pk: Any) -> Optional[KQAnalysis]:
    return get_record(KQAnalysis, pk)

def add_kq_analysis_taxonomy(**kw: Any) -> KQAnalysisTaxonomy:
    return add_record(KQAnalysisTaxonomy, **kw)

def get_kq_analysis_taxonomy_by_id(pk: Any) -> Optional[KQAnalysisTaxonomy]:
    return get_record(KQAnalysisTaxonomy, pk)

def add_kq_analysis_question(**kw: Any) -> KQAnalysisQuestion:
    return add_record(KQAnalysisQuestion, **kw)

def get_kq_analysis_question_by_id(pk: Any) -> Optional[KQAnalysisQuestion]:
    return get_record(KQAnalysisQuestion, pk)