import base64
import hashlib
from typing import <PERSON><PERSON>

from Crypto.Cipher import DES3, AES
from Crypto.Protocol.KDF import PBKDF2
from Crypto.Hash import SHA1

class DecryptionError(Exception):
    """Raised when decryption fails."""
    pass

def _unpad_pkcs7(data: bytes, block_size: int) -> bytes:
    """Strict PKCS#7 unpad."""
    if len(data) == 0 or len(data) % block_size != 0:
        raise ValueError("Invalid data length for PKCS#7")
    pad_len = data[-1]
    if not 1 <= pad_len <= block_size:
        raise ValueError(f"Invalid padding length {pad_len}")
    if data[-pad_len:] != bytes([pad_len]) * pad_len:
        raise ValueError("Invalid PKCS#7 padding bytes")
    return data[:-pad_len]

def _decrypt_v1(key: str, ciphertext_b64: str) -> str:
    """Triple-DES CBC, PKCS7, UTF-16-LE (C# Simple3Des)."""
    ct = base64.b64decode(ciphertext_b64)
    # derive key & IV
    key_bytes = hashlib.sha1(key.encode('utf-16le')).digest()[:24]
    key_bytes = DES3.adjust_key_parity(key_bytes)
    iv_bytes  = hashlib.sha1("".encode('utf-16le')).digest()[:8]

    cipher = DES3.new(key_bytes, DES3.MODE_CBC, iv_bytes)
    pt_padded = cipher.decrypt(ct)
    pt = _unpad_pkcs7(pt_padded, DES3.block_size)
    return pt.decode('utf-16le')

def _decrypt_v2(key: str, encrypted: str) -> str:
    """AES-256-CBC, PKCS7, UTF-8 (Secret.DecryptUsingAesV2)."""
    prefix = "enc:v2:"
    if not encrypted.startswith(prefix):
        raise ValueError("Not a v2 string")
    blob = base64.b64decode(encrypted[len(prefix):])
    salt, iv, ct = blob[:16], blob[16:32], blob[32:]

    dk = PBKDF2(key + "8rF3", salt, dkLen=32, count=10000, hmac_hash_module=SHA1)
    cipher = AES.new(dk, AES.MODE_CBC, iv)
    pt_padded = cipher.decrypt(ct)
    pt = _unpad_pkcs7(pt_padded, AES.block_size)
    return pt.decode('utf-8')

def decrypt_password(key: str, stored_value: str) -> Tuple[bool, str]:
    """
    Try to decrypt `stored_value` with either v1 (3DES) or v2 (AES).
    Returns (True, plaintext) on success, or (False, error_message) on failure.
    """
    try:
        if stored_value.startswith("enc:v2:"):
            plain = _decrypt_v2(key, stored_value)
        elif stored_value.startswith("enc:v"):
            version = stored_value.split(":", 2)[1]
            raise DecryptionError(f"Unsupported enc version: {version!r}")
        else:
            plain = _decrypt_v1(key, stored_value)
        return True, plain

    except DecryptionError as e:
        return False, str(e)
    except Exception as e:
        return False, f"Decryption failed: {e}"


if __name__ == "__main__":
    # Test case
    key = "csg_rw"
    ciphertext = "enc:v2:3mJD6zMcYCx7eLB2IXMfgzDNqu0iZtZbpYU1mfV+c3xRo1f26hyKWh2PNDtRdUz4"

    success, result = decrypt_password(key, ciphertext)
    if success:
        print("Password is:", result)
    else:
        print("Error decrypting:", result)
