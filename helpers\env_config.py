#!/usr/bin/env python3
"""
Centralized environment configuration module for Knowledge Quest Receiver.
Provides standardized environment variable loading, validation, and management.
"""

import os
import logging
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

# Set up logger for this module
logger = logging.getLogger(__name__)


class EnvironmentType(Enum):
    """Supported environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class EnvVarConfig:
    """Configuration for an environment variable."""
    name: str
    description: str
    required: bool = True
    default: Optional[str] = None
    sensitive: bool = False
    validator: Optional[callable] = None


class EnvironmentConfigError(Exception):
    """Raised when environment configuration is invalid."""
    pass


class EnvironmentConfig:
    """Centralized environment configuration manager."""
    
    def __init__(self, env_type: Optional[str] = None):
        self.env_type = env_type or os.getenv('ENVIRONMENT', 'development')
        self.loaded = False
        self.validation_errors: List[str] = []
        self.config_cache: Dict[str, Any] = {}
        
        # Define all environment variables
        self._define_env_vars()
    
    def _define_env_vars(self):
        """Define all environment variables used by the application."""
        self.env_vars = {
            # JWT Configuration
            'JWT_SECRET': EnvVarConfig(
                name='JWT_SECRET',
                description='JWT signing secret key',
                required=True,
                sensitive=True,
                validator=self._validate_jwt_secret
            ),
            'JWT_ISSUER': EnvVarConfig(
                name='JWT_ISSUER',
                description='JWT token issuer',
                required=False,
                default='csi-adapter'
            ),
            'JWT_AUDIENCE': EnvVarConfig(
                name='JWT_AUDIENCE',
                description='JWT token audience',
                required=False,
                default='csi-knowledgequest'
            ),
            
            # Database Configuration
            'DB_TYPE': EnvVarConfig(
                name='DB_TYPE',
                description='Database type (postgres, mssql, snowflake)',
                required=False,
                default='postgres',
                validator=self._validate_db_type
            ),
            'DB_POOL_SIZE': EnvVarConfig(
                name='DB_POOL_SIZE',
                description='Database connection pool size',
                required=False,
                default='5',
                validator=self._validate_positive_int
            ),
            'DB_MAX_OVERFLOW': EnvVarConfig(
                name='DB_MAX_OVERFLOW',
                description='Database max overflow connections',
                required=False,
                default='10',
                validator=self._validate_positive_int
            ),
            'DB_POOL_TIMEOUT': EnvVarConfig(
                name='DB_POOL_TIMEOUT',
                description='Database pool timeout (seconds)',
                required=False,
                default='30',
                validator=self._validate_positive_int
            ),
            'DB_POOL_RECYCLE': EnvVarConfig(
                name='DB_POOL_RECYCLE',
                description='Database connection recycle time (seconds)',
                required=False,
                default='3600',
                validator=self._validate_positive_int
            ),
            'DB_CONNECT_TIMEOUT': EnvVarConfig(
                name='DB_CONNECT_TIMEOUT',
                description='Database connection timeout (seconds)',
                required=False,
                default='10',
                validator=self._validate_positive_int
            ),
            'DB_COMMAND_TIMEOUT': EnvVarConfig(
                name='DB_COMMAND_TIMEOUT',
                description='Database command timeout (seconds)',
                required=False,
                default='30',
                validator=self._validate_positive_int
            ),
            
            # PostgreSQL Configuration
            'POSTGRES_USER': EnvVarConfig(
                name='POSTGRES_USER',
                description='PostgreSQL username',
                required=False  # Only required when DB_TYPE=postgres
            ),
            'POSTGRES_PASS': EnvVarConfig(
                name='POSTGRES_PASS',
                description='PostgreSQL password',
                required=False,
                sensitive=True
            ),
            'POSTGRES_HOST': EnvVarConfig(
                name='POSTGRES_HOST',
                description='PostgreSQL server host',
                required=False
            ),
            'POSTGRES_PORT': EnvVarConfig(
                name='POSTGRES_PORT',
                description='PostgreSQL server port',
                required=False,
                default='5432',
                validator=self._validate_port
            ),
            'POSTGRES_DB': EnvVarConfig(
                name='POSTGRES_DB',
                description='PostgreSQL database name',
                required=False
            ),
            'POSTGRES_SCHEMA': EnvVarConfig(
                name='POSTGRES_SCHEMA',
                description='PostgreSQL schema name',
                required=False,
                default='public'
            ),
            
            # MSSQL Configuration
            'MSSQL_USER': EnvVarConfig(
                name='MSSQL_USER',
                description='MSSQL username',
                required=False
            ),
            'MSSQL_PASS': EnvVarConfig(
                name='MSSQL_PASS',
                description='MSSQL password',
                required=False,
                sensitive=True
            ),
            'MSSQL_HOST': EnvVarConfig(
                name='MSSQL_HOST',
                description='MSSQL server host',
                required=False
            ),
            'MSSQL_DB': EnvVarConfig(
                name='MSSQL_DB',
                description='MSSQL database name',
                required=False
            ),
            'MSSQL_DRIVER': EnvVarConfig(
                name='MSSQL_DRIVER',
                description='MSSQL ODBC driver',
                required=False,
                default='ODBC+Driver+18+for+SQL+Server'
            ),
            
            # Snowflake Configuration
            'SNOWFLAKE_USER': EnvVarConfig(
                name='SNOWFLAKE_USER',
                description='Snowflake username',
                required=False
            ),
            'SNOWFLAKE_PASSWORD': EnvVarConfig(
                name='SNOWFLAKE_PASSWORD',
                description='Snowflake password',
                required=False,
                sensitive=True
            ),
            'SNOWFLAKE_ACCOUNT': EnvVarConfig(
                name='SNOWFLAKE_ACCOUNT',
                description='Snowflake account',
                required=False
            ),
            'SNOWFLAKE_DATABASE': EnvVarConfig(
                name='SNOWFLAKE_DATABASE',
                description='Snowflake database',
                required=False
            ),
            'SNOWFLAKE_SCHEMA': EnvVarConfig(
                name='SNOWFLAKE_SCHEMA',
                description='Snowflake schema',
                required=False
            ),
            'SNOWFLAKE_WAREHOUSE': EnvVarConfig(
                name='SNOWFLAKE_WAREHOUSE',
                description='Snowflake warehouse',
                required=False,
                default=''
            ),
            'SNOWFLAKE_ROLE': EnvVarConfig(
                name='SNOWFLAKE_ROLE',
                description='Snowflake role',
                required=False,
                default=''
            ),
            
            # Application Configuration
            'ENVIRONMENT': EnvVarConfig(
                name='ENVIRONMENT',
                description='Application environment (development, staging, production)',
                required=False,
                default='development',
                validator=self._validate_environment
            ),
            
            # Gunicorn Configuration
            'GUNICORN_WORKERS': EnvVarConfig(
                name='GUNICORN_WORKERS',
                description='Number of Gunicorn worker processes',
                required=False,
                validator=self._validate_positive_int
            ),
            'GUNICORN_TIMEOUT': EnvVarConfig(
                name='GUNICORN_TIMEOUT',
                description='Gunicorn worker timeout (seconds)',
                required=False,
                default='120',
                validator=self._validate_positive_int
            ),
            'GUNICORN_LOG_LEVEL': EnvVarConfig(
                name='GUNICORN_LOG_LEVEL',
                description='Gunicorn log level',
                required=False,
                default='info',
                validator=self._validate_log_level
            ),
        }
    
    def _validate_jwt_secret(self, value: str) -> bool:
        """Validate JWT secret."""
        min_length = 32 if self.env_type == 'production' else 8
        if len(value) < min_length:
            if self.env_type == 'production':
                self.validation_errors.append("JWT_SECRET must be at least 32 characters long in production")
            else:
                self.validation_errors.append("JWT_SECRET must be at least 8 characters long")
            return False
        return True
    
    def _validate_db_type(self, value: str) -> bool:
        """Validate database type."""
        valid_types = ['postgres', 'mssql', 'snowflake']
        if value.lower() not in valid_types:
            self.validation_errors.append(f"DB_TYPE must be one of: {', '.join(valid_types)}")
            return False
        return True
    
    def _validate_positive_int(self, value: str) -> bool:
        """Validate positive integer."""
        try:
            int_val = int(value)
            if int_val <= 0:
                self.validation_errors.append(f"Value must be a positive integer, got: {value}")
                return False
            return True
        except ValueError:
            self.validation_errors.append(f"Value must be an integer, got: {value}")
            return False
    
    def _validate_port(self, value: str) -> bool:
        """Validate port number."""
        try:
            port = int(value)
            if not (1 <= port <= 65535):
                self.validation_errors.append(f"Port must be between 1 and 65535, got: {port}")
                return False
            return True
        except ValueError:
            self.validation_errors.append(f"Port must be an integer, got: {value}")
            return False
    
    def _validate_environment(self, value: str) -> bool:
        """Validate environment type."""
        valid_envs = [e.value for e in EnvironmentType]
        if value.lower() not in valid_envs:
            self.validation_errors.append(f"ENVIRONMENT must be one of: {', '.join(valid_envs)}")
            return False
        return True
    
    def _validate_log_level(self, value: str) -> bool:
        """Validate log level."""
        valid_levels = ['debug', 'info', 'warning', 'error', 'critical']
        if value.lower() not in valid_levels:
            self.validation_errors.append(f"Log level must be one of: {', '.join(valid_levels)}")
            return False
        return True

    def load_environment(self, env_file: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Load environment variables from .env file with comprehensive error handling.

        Args:
            env_file: Optional path to .env file. If None, tries standard locations.

        Returns:
            Tuple of (success, error_messages)
        """
        errors = []

        try:
            from dotenv import load_dotenv

            # Determine which .env file to load
            env_files_to_try = []

            if env_file:
                env_files_to_try.append(env_file)
            else:
                # Try environment-specific files first, then fallback to default
                env_files_to_try.extend([
                    f'.env.{self.env_type}',
                    f'.env.{self.env_type}.local',
                    '.env.local',
                    '.env'
                ])

            loaded_file = None
            for env_path in env_files_to_try:
                if os.path.exists(env_path):
                    try:
                        load_dotenv(env_path, override=True)
                        loaded_file = env_path
                        logger.info(f"✓ Loaded environment variables from {env_path}")
                        break
                    except Exception as e:
                        errors.append(f"Failed to load {env_path}: {e}")
                        continue

            if not loaded_file:
                if env_file:
                    errors.append(f"Specified .env file not found: {env_file}")
                else:
                    logger.warning("⚠ No .env file found, using system environment variables only")

            self.loaded = True
            return True, errors

        except ImportError:
            error_msg = "python-dotenv not installed, .env file will not be loaded automatically"
            logger.warning(f"⚠ {error_msg}")
            errors.append(error_msg)
            self.loaded = True  # Still consider it "loaded" since we can use system env vars
            return True, errors

        except Exception as e:
            error_msg = f"Could not load .env file: {e}"
            logger.error(f"✗ {error_msg}")
            errors.append(error_msg)
            return False, errors

    def validate_configuration(self) -> Tuple[bool, List[str]]:
        """
        Validate all environment variables according to their configuration.

        Returns:
            Tuple of (success, error_messages)
        """
        self.validation_errors.clear()
        missing_required = []

        # Get database type to determine which DB-specific vars are required
        db_type = os.getenv('DB_TYPE', 'postgres').lower()

        for var_name, config in self.env_vars.items():
            value = os.getenv(var_name, config.default)

            # Check if variable is required
            is_required = config.required

            # Special handling for database-specific variables
            if var_name.startswith('POSTGRES_') and db_type != 'postgres':
                is_required = False
            elif var_name.startswith('MSSQL_') and db_type != 'mssql':
                is_required = False
            elif var_name.startswith('SNOWFLAKE_') and db_type != 'snowflake':
                is_required = False
            elif var_name.startswith(('POSTGRES_', 'MSSQL_', 'SNOWFLAKE_')):
                # For database-specific vars, they're required when that DB type is selected
                if var_name.startswith('POSTGRES_') and db_type == 'postgres':
                    is_required = True
                elif var_name.startswith('MSSQL_') and db_type == 'mssql':
                    is_required = True
                elif var_name.startswith('SNOWFLAKE_') and db_type == 'snowflake':
                    is_required = True

            # Check for missing required variables
            if is_required and not value:
                missing_required.append(var_name)
                continue

            # Validate value if present and validator exists
            if value and config.validator:
                if not config.validator(value):
                    # Validation errors are added to self.validation_errors by validators
                    pass

            # Cache the value
            self.config_cache[var_name] = value

        # Add missing required variables to validation errors
        if missing_required:
            self.validation_errors.extend([
                f"{var}: MISSING - {self.env_vars[var].description}"
                for var in missing_required
            ])

        success = len(self.validation_errors) == 0
        return success, self.validation_errors.copy()

    def get(self, var_name: str, default: Optional[str] = None) -> Optional[str]:
        """
        Get an environment variable value with caching.

        Args:
            var_name: Name of the environment variable
            default: Default value if not found

        Returns:
            Environment variable value or default
        """
        if var_name in self.config_cache:
            return self.config_cache[var_name]

        value = os.getenv(var_name, default)
        self.config_cache[var_name] = value
        return value

    def get_int(self, var_name: str, default: int = 0) -> int:
        """Get an environment variable as an integer."""
        value = self.get(var_name, str(default))
        try:
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"Invalid integer value for {var_name}: {value}, using default: {default}")
            return default

    def get_bool(self, var_name: str, default: bool = False) -> bool:
        """Get an environment variable as a boolean."""
        value = self.get(var_name, str(default)).lower()
        return value in ('true', '1', 'yes', 'on', 'enabled')

    def display_configuration(self, mask_sensitive: bool = True) -> None:
        """
        Display current configuration with proper masking of sensitive values.

        Args:
            mask_sensitive: Whether to mask sensitive values
        """
        logger.info("=" * 60)
        logger.info("ENVIRONMENT CONFIGURATION")
        logger.info("=" * 60)
        logger.info(f"Environment Type: {self.env_type}")
        logger.info(f"Configuration Loaded: {self.loaded}")

        # Group variables by category
        categories = {
            'JWT': [k for k in self.env_vars.keys() if k.startswith('JWT_')],
            'Database': [k for k in self.env_vars.keys() if k.startswith('DB_')],
            'PostgreSQL': [k for k in self.env_vars.keys() if k.startswith('POSTGRES_')],
            'MSSQL': [k for k in self.env_vars.keys() if k.startswith('MSSQL_')],
            'Snowflake': [k for k in self.env_vars.keys() if k.startswith('SNOWFLAKE_')],
            'Application': [k for k in self.env_vars.keys() if k in ['ENVIRONMENT']],
            'Gunicorn': [k for k in self.env_vars.keys() if k.startswith('GUNICORN_')],
        }

        db_type = self.get('DB_TYPE', 'postgres').lower()

        for category, var_names in categories.items():
            if not var_names:
                continue

            # Skip irrelevant database categories
            if category == 'PostgreSQL' and db_type != 'postgres':
                continue
            elif category == 'MSSQL' and db_type != 'mssql':
                continue
            elif category == 'Snowflake' and db_type != 'snowflake':
                continue

            logger.info(f"\n{category} Configuration:")
            logger.info("-" * 40)

            for var_name in var_names:
                config = self.env_vars[var_name]
                value = self.get(var_name, config.default)

                if value:
                    # Mask sensitive values
                    if mask_sensitive and config.sensitive:
                        display_value = "***MASKED***"
                    else:
                        display_value = value
                    logger.info(f"✓ {var_name}: {display_value} ({config.description})")
                else:
                    logger.info(f"○ {var_name}: NOT SET ({config.description})")

        logger.info("=" * 60)


# Global instance
env_config = EnvironmentConfig()


def load_environment_config(env_file: Optional[str] = None) -> Tuple[bool, List[str]]:
    """
    Load and validate environment configuration.

    Args:
        env_file: Optional path to .env file

    Returns:
        Tuple of (success, error_messages)
    """
    # Load environment variables
    load_success, load_errors = env_config.load_environment(env_file)

    # Validate configuration
    validate_success, validate_errors = env_config.validate_configuration()

    # Combine results
    all_errors = load_errors + validate_errors
    overall_success = load_success and validate_success

    return overall_success, all_errors


def get_env_config() -> EnvironmentConfig:
    """Get the global environment configuration instance."""
    return env_config
