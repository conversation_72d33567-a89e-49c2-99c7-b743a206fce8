#!/usr/bin/env python3
"""
Database status checker for Knowledge Quest Receiver.
Checks database connectivity, schema, and table existence without making changes.
"""

import os
import logging
from sqlalchemy import text

# Load environment variables using centralized system
from helpers.env_loader import quick_env_setup

# Setup environment with validation
if not quick_env_setup(verbose=True):
    print("✗ Environment setup failed - check configuration and try again")
    exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def check_database_status():
    """Check the complete database status."""
    log.info("=" * 60)
    log.info("KNOWLEDGE QUEST RECEIVER - DATABASE STATUS CHECK")
    log.info("=" * 60)
    
    try:
        from helpers.db import engine, DB_TYPE
        
        # Database connection info
        log.info(f"Database Type: {DB_TYPE}")
        log.info(f"Schema: {os.getenv('POSTGRES_SCHEMA', 'public')}")
        
        # Test connection
        log.info("\n1. Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT version()"))
            version = result.fetchone()[0]
            log.info(f"✓ Connected to: {version.split(',')[0]}")
            
            # Check current schema
            result = conn.execute(text("SELECT current_schema()"))
            current_schema = result.fetchone()[0]
            log.info(f"✓ Current schema: {current_schema}")
            
            # Check search path
            result = conn.execute(text("SHOW search_path"))
            search_path = result.fetchone()[0]
            log.info(f"✓ Search path: {search_path}")
        
        # Check schema existence
        log.info("\n2. Checking schema existence...")
        schema_name = os.getenv('POSTGRES_SCHEMA', 'public')
        
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name = :schema_name
            """), {"schema_name": schema_name})
            
            if result.fetchone():
                log.info(f"✓ Schema '{schema_name}' exists")
            else:
                log.error(f"✗ Schema '{schema_name}' does not exist")
                return False
        
        # Check table existence
        log.info("\n3. Checking table existence...")
        required_tables = ['kq_analysis', 'kq_analysis_taxonomy', 'kq_analysis_question']
        
        with engine.connect() as conn:
            for table_name in required_tables:
                result = conn.execute(text("""
                    SELECT table_name, table_schema
                    FROM information_schema.tables 
                    WHERE table_schema = :schema_name 
                    AND table_name = :table_name
                """), {"schema_name": schema_name, "table_name": table_name})
                
                table_info = result.fetchone()
                if table_info:
                    log.info(f"✓ Table '{table_name}' exists in schema '{table_info[1]}'")
                    
                    # Check row count
                    try:
                        count_result = conn.execute(text(f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"'))
                        count = count_result.fetchone()[0]
                        log.info(f"  └─ Contains {count} rows")
                    except Exception as e:
                        log.warning(f"  └─ Could not count rows: {e}")
                else:
                    log.error(f"✗ Table '{table_name}' does not exist")
                    return False
        
        # Check table structure
        log.info("\n4. Checking table structure...")
        with engine.connect() as conn:
            for table_name in required_tables:
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable
                    FROM information_schema.columns 
                    WHERE table_schema = :schema_name 
                    AND table_name = :table_name
                    ORDER BY ordinal_position
                """), {"schema_name": schema_name, "table_name": table_name})
                
                columns = result.fetchall()
                log.info(f"✓ Table '{table_name}' has {len(columns)} columns:")
                for col in columns:
                    nullable = "NULL" if col[2] == "YES" else "NOT NULL"
                    log.info(f"  └─ {col[0]} ({col[1]}) {nullable}")
        
        log.info("\n" + "=" * 60)
        log.info("✓ DATABASE STATUS CHECK COMPLETED - ALL GOOD!")
        log.info("=" * 60)
        return True
        
    except Exception as e:
        log.error(f"✗ Database status check failed: {e}")
        log.info("\n" + "=" * 60)
        log.error("✗ DATABASE STATUS CHECK FAILED")
        log.error("Run 'python init_database.py' to initialize the database")
        log.info("=" * 60)
        return False

if __name__ == "__main__":
    success = check_database_status()
    exit(0 if success else 1)
