# syntax=docker/dockerfile:1.6   <- enables BuildKit features such as --mount
FROM python:3.12-slim AS runtime

# ----- OS-level dependencies for database drivers -----------------
RUN apt-get update \
 && apt-get install -y --no-install-recommends \
      apt-transport-https \
      unixodbc \
      unixodbc-dev \
      build-essential \
      python3-dev \
 && rm -rf /var/lib/apt/lists/*

# ----- Register Microsoft’s Linux repo ----------------------------
RUN apt-get update && apt-get install -y --no-install-recommends \
        curl gnupg ca-certificates \
 && curl -fsSL https://packages.microsoft.com/keys/microsoft.asc \
      | gpg --dearmor -o /usr/share/keyrings/microsoft.gpg \
 && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/microsoft.gpg] \
        https://packages.microsoft.com/debian/12/prod bookworm main" \
      > /etc/apt/sources.list.d/mssql-release.list

# ----- Install the SQL Server ODBC driver ------------------------
RUN apt-get update \
 && ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql18 \
 && rm -rf /var/lib/apt/lists/*

# ----- Python configuration ---------------------------------------
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

WORKDIR /app
COPY requirements.txt ./

# ----- Python dependencies (cached with BuildKit) -----------------
RUN --mount=type=cache,target=/root/.cache/pip \
    pip install --upgrade pip \
 && pip install -r requirements.txt

# ----- Project source ---------------------------------------------
COPY . .

# ----- Runtime settings -------------------------------------------
EXPOSE 8080
CMD ["gunicorn", "-c", "gunicorn.conf.py", "main:app"]