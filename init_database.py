#!/usr/bin/env python3
"""
Database initialization script for Knowledge Quest Receiver.
Creates all required tables in the PostgreSQL database.
"""

import os
import sys
import logging
from sqlalchemy import text

# Load environment variables using centralized system
from helpers.env_loader import load_env_with_feedback

# Load environment variables from .env file
if not load_env_with_feedback(verbose=True):
    print("✗ Environment loading failed - check .env file configuration")
    exit(1)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
log = logging.getLogger(__name__)

def check_database_connection():
    """Test database connectivity before attempting to create tables."""
    try:
        from helpers.db import engine
        
        log.info("Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone()[0] == 1:
                log.info("✓ Database connection successful")
                return True
            else:
                log.error("✗ Database connection test failed")
                return False
    except Exception as e:
        log.error(f"✗ Database connection failed: {e}")
        return False



def validate_schema_exists():
    """Validate that the required schema exists - fail if it doesn't."""
    try:
        # Import here to get fresh engine with current environment variables
        import importlib
        import helpers.db
        importlib.reload(helpers.db)  # Reload to pick up current environment variables
        from helpers.db import engine

        # Get current schema name from environment
        schema_name = os.getenv('POSTGRES_SCHEMA', 'public')
        if schema_name == 'public':
            log.info("Using default 'public' schema - validation not required")
            return True

        log.info(f"Validating that schema '{schema_name}' exists...")

        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT schema_name
                FROM information_schema.schemata
                WHERE schema_name = :schema_name
            """), {"schema_name": schema_name})

            if result.fetchone():
                log.info(f"✓ Schema '{schema_name}' exists and is accessible")
                return True
            else:
                log.error(f"✗ Schema '{schema_name}' does not exist")
                log.error("Please create the schema manually or ask your database administrator to create it:")
                log.error(f"  SQL: CREATE SCHEMA \"{schema_name}\";")
                return False

    except Exception as e:
        log.error(f"✗ Error validating schema: {e}")
        return False

def check_tables_exist():
    """Check if the required tables already exist."""
    try:
        from helpers.db import engine
        
        schema_name = os.getenv('POSTGRES_SCHEMA', 'public')
        log.info(f"Checking if tables exist in schema '{schema_name}'...")
        
        required_tables = ['kq_analysis', 'kq_analysis_taxonomy', 'kq_analysis_question']
        existing_tables = []
        
        with engine.connect() as conn:
            for table_name in required_tables:
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = :schema_name 
                    AND table_name = :table_name
                """), {"schema_name": schema_name, "table_name": table_name})
                
                if result.fetchone():
                    existing_tables.append(table_name)
                    log.info(f"✓ Table '{table_name}' exists")
                else:
                    log.warning(f"⚠ Table '{table_name}' does not exist")
        
        if len(existing_tables) == len(required_tables):
            log.info("✓ All required tables exist")
            return True
        else:
            log.warning(f"Missing tables: {set(required_tables) - set(existing_tables)}")
            return False
            
    except Exception as e:
        log.error(f"✗ Error checking tables: {e}")
        return False

def create_tables():
    """Create all database tables using SQLAlchemy models."""
    try:
        from helpers.db import engine
        from helpers.db_models import Base
        
        log.info("Creating database tables...")
        
        # Create all tables defined in the models
        Base.metadata.create_all(bind=engine)
        
        log.info("✓ Database tables created successfully")
        return True
        
    except Exception as e:
        log.error(f"✗ Error creating tables: {e}")
        return False

def verify_tables():
    """Verify that all tables were created successfully."""
    try:
        from helpers.db import engine
        
        schema_name = os.getenv('POSTGRES_SCHEMA', 'public')
        log.info("Verifying table creation...")
        
        with engine.connect() as conn:
            # Test each table with a simple query
            tables_to_test = ['kq_analysis', 'kq_analysis_taxonomy', 'kq_analysis_question']
            
            for table_name in tables_to_test:
                try:
                    result = conn.execute(text(f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"'))
                    count = result.fetchone()[0]
                    log.info(f"✓ Table '{table_name}' verified - contains {count} rows")
                except Exception as e:
                    log.error(f"✗ Error verifying table '{table_name}': {e}")
                    return False
        
        log.info("✓ All tables verified successfully")
        return True
        
    except Exception as e:
        log.error(f"✗ Error during table verification: {e}")
        return False

def main():
    """Main initialization function."""
    log.info("=" * 60)
    log.info("KNOWLEDGE QUEST RECEIVER - DATABASE INITIALIZATION")
    log.info("=" * 60)
    
    # Step 1: Check database connection
    if not check_database_connection():
        log.error("Cannot proceed without database connection")
        sys.exit(1)
    
    # Step 2: Validate schema exists
    if not validate_schema_exists():
        log.error("Cannot proceed without required schema")
        sys.exit(1)
    
    # Step 3: Check if tables already exist
    if check_tables_exist():
        log.info("All required tables already exist - no action needed")
        return
    
    # Step 4: Create tables
    if not create_tables():
        log.error("Failed to create database tables")
        sys.exit(1)
    
    # Step 5: Verify tables
    if not verify_tables():
        log.error("Table verification failed")
        sys.exit(1)
    
    log.info("=" * 60)
    log.info("✓ DATABASE INITIALIZATION COMPLETED SUCCESSFULLY")
    log.info("=" * 60)

if __name__ == "__main__":
    main()
