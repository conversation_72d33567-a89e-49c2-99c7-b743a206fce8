import datetime, jwt, requests, json, os

def generate_jwt_token(secret, subject, expiration_minutes=5):
    payload = {
        'sub': subject,  # name of the adapter customer, or something descriptive
        'iat': datetime.datetime.now(datetime.timezone.utc),
        'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(minutes=expiration_minutes),
        'iss': 'csi-adapter',
        'aud': 'csi-knowledgequest'
    }
    token = jwt.encode(payload, secret, algorithm='HS256')
    return token

token = generate_jwt_token(secret="supersecret",
                           subject="customer")

url="http://localhost:8080"
payload = {
  "kq_analysisid": "926547c4-1498-4c00-9f08-998b2864b2d8",
  "conversationid": "54cc5998-2720-468f-886c-e6030692ecf9",
  "communicationid": "a5d18897-d82a-3541-b4e8-ad1312a4760f",
  "call_summary": "The caller reported a partial power outage affecting terminals and alarms at their location, and the agent created a work order for an electrician to investigate the issue.",
  "call_resolution": "Resolved",
  "callback_required": True,
  "selfservice_attempted": False,
  "selfservice_problems": False,
  "satisfaction_sentiment": 75.00,
  "taxonomies": [
    {
      "kq_analysistaxonomyid": "804bdba3-6c6f-4483-bb6d-c14d8ce67170",
      "taxonomy": "Building Exterior    Equipment"
    },
    {
      "kq_analysistaxonomyid": "80dff955-38de-4c3e-bad8-217bce482f61",
      "taxonomy": "Recurring           Utilities"
    },
    {
      "kq_analysistaxonomyid": "a15a5092-e7d9-4650-aeec-dcf167e71b57",
      "taxonomy": "Building Interior    Equipment"
    }
  ],
  "questions": [
    {
      "kq_analysisquestionid": "56e8058f-06d1-4e47-8a46-4cfa7641a6cc",
      "question": "How do I handle customer requests for updates on emergency service calls?",
      "answer": "Create a work order for the emergency service request. Confirm the details of the outage with the caller. Check the availability of an electrician to attend to the issue. Inform the caller about the status of the work order and next steps. Ask the caller for a contact number to reach them if needed. Contact the facility manager to expedite the service request. Provide updates to the caller as needed.",
      "taxonomy": "Other               Other",
      "knowledgeid": None,
      "knowledge_confidence": None,
    },
    {
      "kq_analysisquestionid": "f589e063-a8ed-4391-9c1c-cdd78e2b3b45",
      "question": "What is the procedure for contacting a facility manager regarding urgent electrical issues?",
      "answer": "The agent confirmed the urgency of the electrical issue and indicated they would call the facility manager to discuss how soon an electrician could be sent. The agent informed the caller that the work order was set under a priority 1 emergency. The agent assured the caller that they would follow up after contacting the facility manager.",
      "taxonomy": "Building Exterior    Environmental, Health & Safety",
      "knowledgeid": None,
      "knowledge_confidence": None,
    }
  ]
}

headers = {
    "Authorization": f"Bearer {token}",
}
response = requests.post(url, json=payload, headers=headers)