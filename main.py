import logging
import os
import jwt
from pydantic import ValidationError

from flask import Flask, request, jsonify

from helpers.db_crud import (
    get_kq_analysis_by_id,
    add_kq_analysis,
    get_kq_analysis_taxonomy_by_id,
    add_kq_analysis_taxonomy,
    get_kq_analysis_question_by_id,
    add_kq_analysis_question,
    DataAccessError,
)
from helpers.receiving_models import (
    Analysis,
    AnalysisQuestion,
    AnalysisTaxonomy
)

formatter = logging.Formatter(
    '%(process)d:%(asctime)s - %(module)s:%(name)s - %(filename)s:%(lineno)s - '
    '%(funcName)20s() - %(levelname)s - %(message)s')
log = logging.getLogger()
log.setLevel(logging.DEBUG)
ch = logging.StreamHandler()
ch.setLevel(logging.DEBUG)
ch.setFormatter(formatter)
logging.getLogger().handlers.clear()
log.addHandler(ch)

log.info("""
************************************************************
#   #  #   #   ###   #   #  #     #####  ####   ####   #####
#  #   ##  #  #   #  #   #  #     #      #   #  #      #    
# #    # # #  #   #  #   #  #     #      #   #  #      #    
##     #  ##  #   #  # # #  #     ####   #   #  #  ##  #### 
# #    #   #  #   #  # # #  #     #      #   #  #   #  #    
#  #   #   #  #   #  # # #  #     #      #   #  #   #  #    
#   #  #   #   ###     #    ##### #####  #####   ###   #####

 ###  #   # #####  #### #####
#   # #   # #      #      #  
#   # #   # #      #      #  
#   # #   # ####   ####   #                        : Receiver
# # # #   # #         #   #                 
#  #  #   # #         #   #  
 ## #  ###  #####  ####   #
************************************************************

Starting...
""")

JWT_SECRET   = os.environ["JWT_SECRET"]
JWT_ISSUER   = os.getenv("JWT_ISSUER",   "csi-adapter")
JWT_AUDIENCE = os.getenv("JWT_AUDIENCE", "csi-knowledgequest")

app = Flask(__name__)

def verify_jwt_token(token: str) -> dict | None:
    """
    Verifies and decodes the given JWT token against the 'jwt_secret' environment variable.
    Returns the payload if valid, or None if invalid or missing the secret.
    """

    try:
        # Validate header
        hdr = jwt.get_unverified_header(token)
        if hdr.get("alg") != "HS256":
            log.error("Unexpected JWT alg: %s", hdr.get("alg"))
            return None

        # Validate token with required claims
        payload = jwt.decode(
            token,
            JWT_SECRET,
            algorithms=['HS256'],
            issuer=JWT_ISSUER,
            audience=JWT_AUDIENCE
        )
        return payload
    except jwt.ExpiredSignatureError:
        log.error("JWT token has expired.")
        return None
    except jwt.InvalidTokenError:
        log.error("JWT token is invalid.")
        return None


@app.route("/", defaults={"path": ""}, methods=["POST"])
def kq_incoming(path):
    log.info(f"{request.path} - Incoming request")

    body_json = request.get_json(silent=True)
    if body_json is None:
        return jsonify({"error": "Invalid JSON payload"}), 400

    # JWT Auth
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        return jsonify({"error": "Authorization header missing"}), 401
    parts = auth_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return jsonify({"error": "Invalid Authorization header format"}), 401
    if verify_jwt_token(parts[1]) is None:
        return jsonify({"error": "Invalid or expired token"}), 401

    log.info(f"JWT verified, header: {auth_header}")

    # Verify the incoming payload against the exepcted model
    try:
        incoming_analysis = Analysis.model_validate(body_json)
    except ValidationError as e:
        return jsonify({"error": "Validation failed", "details": e.errors()}), 422

    # Add the analysis to the CSI database
    try:
        existing_analysis = get_kq_analysis_by_id(incoming_analysis.kq_analysisid)
    except DataAccessError as exc:
        log.exception("Failed to check existing KQAnalysis")
        return jsonify({"error": "Database failure"}), 500

    if existing_analysis:
        log.info("Skipping existing KQAnalysis: %r", existing_analysis)
        return jsonify({"success": "KQAnalysis already exists"}), 200
    else:
        try:
            add_kq_analysis(
                id=incoming_analysis.kq_analysisid,
                conversationid=incoming_analysis.conversationid,
                communicationid=incoming_analysis.communicationid,
                callsummary=incoming_analysis.call_summary,
                callresolution=incoming_analysis.call_resolution,
                callbackrequired=incoming_analysis.callback_required,
                selfserviceattempted=incoming_analysis.selfservice_attempted,
                selfserviceproblems=incoming_analysis.selfservice_problems,
                satisfactionsentiment=incoming_analysis.satisfaction_sentiment,
            )
            log.info("Created new KQAnalysis: %r", incoming_analysis.kq_analysisid)
        except DataAccessError as exc:
            log.exception("Failed to create KQAnalysis")
            return jsonify({"error": "Database failure"}), 500

    # Add the taxonomy associated to the analysis
    for tax in incoming_analysis.taxonomies:
        if get_kq_analysis_taxonomy_by_id(tax.kq_analysistaxonomyid):
            continue
        try:
            add_kq_analysis_taxonomy(
                id=tax.kq_analysistaxonomyid,
                kq_analysisid=incoming_analysis.kq_analysisid,
                taxonomy=tax.taxonomy,
            )
        except DataAccessError:
            log.exception("Failed to add taxonomy %s", tax.kq_analysistaxonomyid)

    # Add the questions associated to the analysis
    for q in incoming_analysis.questions:
        if get_kq_analysis_question_by_id(q.kq_analysisquestionid):
            continue
        try:
            add_kq_analysis_question(
                id=q.kq_analysisquestionid,
                kq_analysisid=incoming_analysis.kq_analysisid,
                question=q.question,
                answer=q.answer,
                taxonomy=q.taxonomy,
                knowledgeid=q.knowledgeid,
                knowledge_confidence=q.knowledge_confidence,
            )
        except DataAccessError:
            log.exception("Failed to add question %s", q.kq_analysisquestionid)

    return jsonify({"success": "KQAnalysis created"}), 201


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080, debug=False)
    log.info("Started.")