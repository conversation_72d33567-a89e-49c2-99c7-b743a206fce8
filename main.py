import logging
import os
import jwt
import time
import psutil
from datetime import datetime, timezone
from pydantic import ValidationError

# Load environment variables using centralized system
from helpers.env_loader import quick_env_setup

# Quick environment setup with validation
if not quick_env_setup(verbose=True):
    print("✗ Environment setup failed - check configuration and try again")
    exit(1)

from flask import Flask, request, jsonify

from helpers.db_crud import (
    get_kq_analysis_by_id,
    add_kq_analysis,
    get_kq_analysis_taxonomy_by_id,
    add_kq_analysis_taxonomy,
    get_kq_analysis_question_by_id,
    add_kq_analysis_question,
    DataAccessError,
)
from helpers.receiving_models import (
    Analysis,
    AnalysisQuestion,
    AnalysisTaxonomy
)
from helpers.logging_utils import (
    setup_structured_logging,
    generate_correlation_id,
    set_correlation_id,
    get_correlation_id,
    log_request_timing
)

# Set up structured logging
log = setup_structured_logging()

def validate_and_display_environment():
    """Validate and display environment variables at startup."""
    log.info("=" * 60)
    log.info("ENVIRONMENT VARIABLE VALIDATION")
    log.info("=" * 60)

    # Required environment variables
    required_vars = {
        'JWT_SECRET': 'JWT signing secret key',
    }

    # Optional environment variables with defaults
    optional_vars = {
        'JWT_ISSUER': ('JWT token issuer', 'csi-adapter'),
        'JWT_AUDIENCE': ('JWT token audience', 'csi-knowledgequest'),
        'DB_TYPE': ('Database type', 'mssql'),
        'DB_POOL_SIZE': ('Database connection pool size', '5'),
        'DB_MAX_OVERFLOW': ('Database max overflow connections', '10'),
        'DB_POOL_TIMEOUT': ('Database pool timeout (seconds)', '30'),
        'DB_POOL_RECYCLE': ('Database connection recycle time (seconds)', '3600'),
        'DB_CONNECT_TIMEOUT': ('Database connection timeout (seconds)', '10'),
        'DB_COMMAND_TIMEOUT': ('Database command timeout (seconds)', '30'),
    }

    # Check required variables
    missing_required = []
    for var_name, description in required_vars.items():
        value = os.getenv(var_name)
        if value:
            # Mask sensitive values
            display_value = "***MASKED***" if "SECRET" in var_name or "PASS" in var_name else value
            log.info(f"✓ {var_name}: {display_value} ({description})")
        else:
            log.error(f"✗ {var_name}: MISSING - {description}")
            missing_required.append(var_name)

    # Check optional variables
    for var_name, (description, default) in optional_vars.items():
        value = os.getenv(var_name, default)
        log.info(f"✓ {var_name}: {value} ({description})")

    # Database-specific variables based on DB_TYPE
    db_type = os.getenv('DB_TYPE', 'mssql').lower()
    log.info(f"\nDatabase-specific configuration for: {db_type.upper()}")
    log.info(f"DB_TYPE environment variable: '{os.getenv('DB_TYPE', 'NOT SET')}'")
    log.info("-" * 40)

    db_vars = []
    if db_type == 'mssql':
        db_vars = [
            ('MSSQL_USER', 'MSSQL username', True),
            ('MSSQL_PASS', 'MSSQL password', True),
            ('MSSQL_HOST', 'MSSQL server host', True),
            ('MSSQL_DB', 'MSSQL database name', True),
            ('MSSQL_DRIVER', 'MSSQL ODBC driver', False, 'ODBC+Driver+18+for+SQL+Server'),
        ]
    elif db_type == 'postgres':
        db_vars = [
            ('POSTGRES_USER', 'PostgreSQL username', True),
            ('POSTGRES_PASS', 'PostgreSQL password', True),
            ('POSTGRES_HOST', 'PostgreSQL server host', True),
            ('POSTGRES_PORT', 'PostgreSQL server port', True),
            ('POSTGRES_DB', 'PostgreSQL database name', True),
            ('POSTGRES_SCHEMA', 'PostgreSQL schema name', False, 'public'),
        ]
    elif db_type == 'snowflake':
        db_vars = [
            ('SNOWFLAKE_USER', 'Snowflake username', True),
            ('SNOWFLAKE_PASSWORD', 'Snowflake password', True),
            ('SNOWFLAKE_ACCOUNT', 'Snowflake account', True),
            ('SNOWFLAKE_DATABASE', 'Snowflake database', True),
            ('SNOWFLAKE_SCHEMA', 'Snowflake schema', True),
            ('SNOWFLAKE_WAREHOUSE', 'Snowflake warehouse', False, ''),
            ('SNOWFLAKE_ROLE', 'Snowflake role', False, ''),
        ]

    for var_info in db_vars:
        if len(var_info) == 3:
            var_name, description, required = var_info
            default = None
        else:
            var_name, description, required, default = var_info

        value = os.getenv(var_name, default)
        if value:
            # Mask sensitive values
            display_value = "***MASKED***" if "PASS" in var_name or "PASSWORD" in var_name else value
            log.info(f"✓ {var_name}: {display_value} ({description})")
        elif required:
            log.error(f"✗ {var_name}: MISSING - {description}")
            missing_required.append(var_name)
        else:
            log.info(f"○ {var_name}: NOT SET ({description}) - Optional")

    log.info("=" * 60)

    if missing_required:
        log.error(f"STARTUP FAILED: Missing required environment variables: {', '.join(missing_required)}")
        log.error("Please set the required environment variables and restart the application.")
        raise SystemExit(1)
    else:
        log.info("✓ All required environment variables are present")
        log.info("=" * 60)

# Validate environment variables at startup
validate_and_display_environment()

def test_database_connection():
    """Test database connectivity during startup."""
    log.info("=" * 60)
    log.info("DATABASE CONNECTION TEST")
    log.info("=" * 60)

    try:
        from helpers.db import engine, DB_TYPE
        from sqlalchemy import text

        log.info(f"Testing connection to {DB_TYPE.upper()} database...")

        # Test basic connectivity
        with engine.connect() as conn:
            # Use a simple query that works across all database types
            result = conn.execute(text("SELECT 1 as test_value"))
            test_row = result.fetchone()

            if test_row and test_row[0] == 1:
                log.info("✓ Database connection successful")

                # Get additional database info if possible
                try:
                    if DB_TYPE == "postgres":
                        version_result = conn.execute(text("SELECT version()"))
                        version = version_result.fetchone()[0]
                        log.info(f"✓ PostgreSQL Version: {version.split(',')[0]}")
                    elif DB_TYPE == "mssql":
                        version_result = conn.execute(text("SELECT @@VERSION"))
                        version = version_result.fetchone()[0]
                        # Extract just the first line for cleaner output
                        version_line = version.split('\n')[0].strip()
                        log.info(f"✓ SQL Server Version: {version_line}")
                    elif DB_TYPE == "snowflake":
                        version_result = conn.execute(text("SELECT CURRENT_VERSION()"))
                        version = version_result.fetchone()[0]
                        log.info(f"✓ Snowflake Version: {version}")
                except Exception as e:
                    log.warning(f"Could not retrieve database version info: {e}")

                # Test connection pool
                pool = engine.pool
                log.info(f"✓ Connection pool configured: size={pool.size()}, checked_out={pool.checkedout()}")

            else:
                log.error("✗ Database test query failed")
                raise Exception("Database test query returned unexpected result")

    except Exception as e:
        log.error(f"✗ Database connection failed: {e}")
        log.error("Please check your database configuration and credentials.")
        log.error("The application will continue but database operations will fail.")
        # Don't exit here - let the application start but warn about DB issues
        return False

    log.info("=" * 60)
    return True

# Test database connection at startup
database_available = test_database_connection()

log.info("""
************************************************************
#   #  #   #   ###   #   #  #     #####  ####   ####   #####
#  #   ##  #  #   #  #   #  #     #      #   #  #      #    
# #    # # #  #   #  #   #  #     #      #   #  #      #    
##     #  ##  #   #  # # #  #     ####   #   #  #  ##  #### 
# #    #   #  #   #  # # #  #     #      #   #  #   #  #    
#  #   #   #  #   #  # # #  #     #      #   #  #   #  #    
#   #  #   #   ###     #    ##### #####  #####   ###   #####

 ###  #   # #####  #### #####
#   # #   # #      #      #  
#   # #   # #      #      #  
#   # #   # ####   ####   #                        : Receiver
# # # #   # #         #   #                 
#  #  #   # #         #   #  
 ## #  ###  #####  ####   #
************************************************************

Starting...
""")

JWT_SECRET   = os.environ["JWT_SECRET"]
JWT_ISSUER   = os.getenv("JWT_ISSUER",   "csi-adapter")
JWT_AUDIENCE = os.getenv("JWT_AUDIENCE", "csi-knowledgequest")

app = Flask(__name__)

# Application metrics tracking
app_start_time = datetime.now(timezone.utc)
request_count = 0
error_count = 0
database_available = False  # Will be set during startup

def error_response(message: str, status_code: int, correlation_id: str = None):
    """Helper function to create error responses and increment error count."""
    global error_count
    error_count += 1

    response_data = {"error": message}
    if correlation_id:
        response_data["correlation_id"] = correlation_id

    return jsonify(response_data), status_code

def handle_database_error(db_error, operation_context: str, correlation_id: str = None):
    """
    Handle DataAccessError with appropriate HTTP status codes and clean error messages.
    """
    if hasattr(db_error, 'error_type'):
        if db_error.error_type == "schema_missing":
            log.error(f"Database schema not initialized during {operation_context}")
            return error_response(
                "Service temporarily unavailable - database schema not initialized",
                503,
                correlation_id
            )
        elif db_error.error_type == "connection":
            log.error(f"Database connection error during {operation_context}")
            return error_response(
                "Service temporarily unavailable - database connection error",
                503,
                correlation_id
            )
        elif db_error.error_type == "permission":
            log.error(f"Database permission error during {operation_context}")
            return error_response(
                "Service temporarily unavailable - database access error",
                503,
                correlation_id
            )

    # Generic database error
    log.error(f"Database error during {operation_context}: {str(db_error)}")
    return error_response(
        "Database operation failed",
        500,
        correlation_id
    )

def verify_jwt_token(token: str) -> dict | None:
    """
    Verifies and decodes the given JWT token against the 'jwt_secret' environment variable.
    Returns the payload if valid, or None if invalid or missing the secret.
    """

    try:
        # Validate header
        hdr = jwt.get_unverified_header(token)
        if hdr.get("alg") != "HS256":
            log.error("Unexpected JWT alg: %s", hdr.get("alg"))
            return None

        # Validate token with required claims
        payload = jwt.decode(
            token,
            JWT_SECRET,
            algorithms=['HS256'],
            issuer=JWT_ISSUER,
            audience=JWT_AUDIENCE
        )
        return payload
    except jwt.ExpiredSignatureError:
        log.error("JWT token has expired.")
        return None
    except jwt.InvalidTokenError:
        log.error("JWT token is invalid.")
        return None


@app.route("/health", methods=["GET"])
def health_check():
    """Health check endpoint for monitoring and load balancers."""
    global database_available

    try:
        # Get system metrics
        process = psutil.Process()
        memory_info = process.memory_info()

        uptime = datetime.now(timezone.utc) - app_start_time

        # Test database connectivity if it was available at startup
        db_status = "unknown"
        if database_available:
            try:
                from helpers.db import engine
                from sqlalchemy import text
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                db_status = "connected"
            except Exception as db_e:
                log.warning(f"Database health check failed: {db_e}")
                db_status = "disconnected"
        else:
            db_status = "unavailable"

        # Determine overall health status
        overall_status = "healthy" if db_status == "connected" else "degraded"
        status_code = 200 if db_status == "connected" else 503

        health_data = {
            "status": overall_status,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "uptime_seconds": int(uptime.total_seconds()),
            "database": db_status,
            "memory_mb": round(memory_info.rss / 1024 / 1024, 1),
            "cpu_percent": process.cpu_percent(),
            "request_count": request_count,
            "error_count": error_count
        }

        return jsonify(health_data), status_code

    except Exception as e:
        log.exception("Health check failed")
        return jsonify({
            "status": "unhealthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "error": str(e)
        }), 503


@app.route("/metrics", methods=["GET"])
def metrics():
    """Detailed metrics endpoint for monitoring systems."""
    try:
        process = psutil.Process()
        memory_info = process.memory_info()
        cpu_times = process.cpu_times()

        uptime = datetime.now(timezone.utc) - app_start_time

        metrics_data = {
            "application": {
                "name": "kq-receiver",
                "uptime_seconds": int(uptime.total_seconds()),
                "start_time": app_start_time.isoformat(),
                "request_count": request_count,
                "error_count": error_count,
                "error_rate": round(error_count / max(request_count, 1) * 100, 2)
            },
            "system": {
                "memory": {
                    "rss_mb": round(memory_info.rss / 1024 / 1024, 1),
                    "vms_mb": round(memory_info.vms / 1024 / 1024, 1),
                    "percent": psutil.virtual_memory().percent
                },
                "cpu": {
                    "percent": process.cpu_percent(),
                    "user_time": cpu_times.user,
                    "system_time": cpu_times.system
                },
                "connections": len(process.net_connections())
            },
            "database": {
                "pool_size": os.getenv('DB_POOL_SIZE', '5'),
                "max_overflow": os.getenv('DB_MAX_OVERFLOW', '10')
            }
        }

        return jsonify(metrics_data), 200

    except Exception as e:
        log.exception("Metrics collection failed")
        return jsonify({"error": str(e)}), 500


@app.route("/", defaults={"path": ""}, methods=["POST"])
@log_request_timing
def kq_incoming(path):
    global request_count, error_count
    request_count += 1

    # Generate and set correlation ID for this request
    corr_id = generate_correlation_id()
    set_correlation_id(corr_id)

    log.info(f"{request.path} - Incoming request - Correlation ID: {corr_id}")

    try:
        body_json = request.get_json(silent=True)
        if body_json is None:
            log.warning("Request failed: Invalid JSON payload")
            return error_response("Invalid JSON payload", 400, get_correlation_id())

        # JWT Auth
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            log.warning("Request failed: Authorization header missing")
            return error_response("Authorization header missing", 401, get_correlation_id())

        parts = auth_header.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            log.warning("Request failed: Invalid Authorization header format")
            return error_response("Invalid Authorization header format", 401, get_correlation_id())

        jwt_payload = verify_jwt_token(parts[1])
        if jwt_payload is None:
            log.warning("Request failed: Invalid or expired JWT token")
            return error_response("Invalid or expired token", 401, get_correlation_id())

        log.info(f"JWT verified successfully for subject: {jwt_payload.get('sub', 'unknown')}")

    except Exception as e:
        log.exception("Unexpected error during request validation")
        return jsonify({"error": "Internal server error", "correlation_id": get_correlation_id()}), 500

    # Verify the incoming payload against the expected model
    try:
        incoming_analysis = Analysis.model_validate(body_json)
        log.info(f"Payload validation successful for analysis ID: {incoming_analysis.kq_analysisid}")
    except ValidationError as e:
        log.warning(f"Payload validation failed: {e.error_count()} errors found")
        log.debug(f"Validation errors: {e.errors()}")
        return jsonify({
            "error": "Validation failed",
            "details": e.errors(),
            "correlation_id": get_correlation_id()
        }), 422
    except Exception as e:
        log.exception("Unexpected error during payload validation")
        return jsonify({
            "error": "Internal server error during validation",
            "correlation_id": get_correlation_id()
        }), 500

    # Add the analysis to the CSI database
    try:
        existing_analysis = get_kq_analysis_by_id(incoming_analysis.kq_analysisid)
        log.debug(f"Checked for existing analysis: {incoming_analysis.kq_analysisid}, found: {existing_analysis is not None}")
    except DataAccessError as db_error:
        return handle_database_error(db_error, "analysis lookup", get_correlation_id())
    except Exception as e:
        log.exception(f"Unexpected error while checking existing KQAnalysis: {incoming_analysis.kq_analysisid}")
        return jsonify({
            "error": "Internal server error during analysis lookup",
            "correlation_id": get_correlation_id()
        }), 500

    if existing_analysis:
        log.info(f"Skipping existing KQAnalysis: {existing_analysis.id}")
        return jsonify({"success": "KQAnalysis already exists", "correlation_id": get_correlation_id()}), 200
    else:
        try:
            add_kq_analysis(
                id=incoming_analysis.kq_analysisid,
                conversationid=incoming_analysis.conversationid,
                communicationid=incoming_analysis.communicationid,
                callsummary=incoming_analysis.call_summary,
                callresolution=incoming_analysis.call_resolution,
                callbackrequired=incoming_analysis.callback_required,
                selfserviceattempted=incoming_analysis.selfservice_attempted,
                selfserviceproblems=incoming_analysis.selfservice_problems,
                satisfactionsentiment=incoming_analysis.satisfaction_sentiment,
            )
            log.info(f"Created new KQAnalysis: {incoming_analysis.kq_analysisid}")
        except DataAccessError as db_error:
            return handle_database_error(db_error, "analysis creation", get_correlation_id())
        except Exception as e:
            log.exception(f"Unexpected error while creating KQAnalysis: {incoming_analysis.kq_analysisid}")
            return jsonify({
                "error": "Internal server error during analysis creation",
                "correlation_id": get_correlation_id()
            }), 500

    # Add the taxonomy associated to the analysis
    taxonomy_errors = []
    for tax in incoming_analysis.taxonomies:
        try:
            if get_kq_analysis_taxonomy_by_id(tax.kq_analysistaxonomyid):
                log.debug(f"Taxonomy already exists: {tax.kq_analysistaxonomyid}")
                continue

            add_kq_analysis_taxonomy(
                id=tax.kq_analysistaxonomyid,
                kq_analysisid=incoming_analysis.kq_analysisid,
                taxonomy=tax.taxonomy,
            )
            log.debug(f"Added taxonomy: {tax.kq_analysistaxonomyid}")
        except DataAccessError as db_error:
            if hasattr(db_error, 'error_type') and db_error.error_type == "schema_missing":
                error_msg = "Database schema not initialized"
                log.error(f"Schema error adding taxonomy {tax.kq_analysistaxonomyid}: {error_msg}")
            else:
                error_msg = f"Failed to add taxonomy {tax.kq_analysistaxonomyid}"
                log.error(f"Database error adding taxonomy: {error_msg}")
            taxonomy_errors.append(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error adding taxonomy {tax.kq_analysistaxonomyid}: {str(e)}"
            log.exception(error_msg)
            taxonomy_errors.append(error_msg)

    # Add the questions associated to the analysis
    question_errors = []
    for q in incoming_analysis.questions:
        try:
            if get_kq_analysis_question_by_id(q.kq_analysisquestionid):
                log.debug(f"Question already exists: {q.kq_analysisquestionid}")
                continue

            add_kq_analysis_question(
                id=q.kq_analysisquestionid,
                kq_analysisid=incoming_analysis.kq_analysisid,
                question=q.question,
                answer=q.answer,
                taxonomy=q.taxonomy,
                knowledgeid=q.knowledgeid,
                knowledge_confidence=q.knowledge_confidence,
            )
            log.debug(f"Added question: {q.kq_analysisquestionid}")
        except DataAccessError as db_error:
            if hasattr(db_error, 'error_type') and db_error.error_type == "schema_missing":
                error_msg = "Database schema not initialized"
                log.error(f"Schema error adding question {q.kq_analysisquestionid}: {error_msg}")
            else:
                error_msg = f"Failed to add question {q.kq_analysisquestionid}"
                log.error(f"Database error adding question: {error_msg}")
            question_errors.append(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error adding question {q.kq_analysisquestionid}: {str(e)}"
            log.exception(error_msg)
            question_errors.append(error_msg)

    # Prepare response with any warnings
    response_data = {
        "success": "KQAnalysis created",
        "correlation_id": get_correlation_id(),
        "analysis_id": incoming_analysis.kq_analysisid
    }

    if taxonomy_errors or question_errors:
        response_data["warnings"] = {
            "taxonomy_errors": taxonomy_errors,
            "question_errors": question_errors
        }
        log.warning(f"Analysis created with warnings: {len(taxonomy_errors)} taxonomy errors, {len(question_errors)} question errors")

    return jsonify(response_data), 201


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=8080, debug=False)
    log.info("Started.")