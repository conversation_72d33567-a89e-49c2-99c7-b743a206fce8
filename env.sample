# JWT details
JWT_SECRET=cd462.....d1ebc
JWT_ISSUER=csi-adapter
JWT_AUDIENCE=csi-knowledgequest

# Customer Database Type (postgres | mssql | snowflake)
DB_TYPE=mssql

# Postgres connection details
POSTGRES_USER=user
POSTGRES_PASS=pass
POSTGRES_DB=database
POSTGRES_HOST=host
POSTGRES_PORT=5432


# MSSQL connection details
MSSQL_USER=kq_processor
MSSQL_PASS=abcd
MSSQL_HOST=customerscience-csi.database.windows.net
MSSQL_DB=knowledgequest
MSSQL_DRIVER=ODBC+Driver+18+for+SQL+Server

# Snowflake connection details
SNOWFLAKE_USER=user
SNOWFLAKE_PASSWORD=pass
SNOWFLAKE_ACCOUNT=account
SNOWFLAKE_DATABASE=database
SNOWFLAKE_SCHEMA=schema
SNOWFLAKE_WAREHOUSE=warehouse # optional
SNOWFLAKE_ROLE=role # optional